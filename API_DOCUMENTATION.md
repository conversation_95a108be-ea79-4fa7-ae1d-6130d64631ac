# 解绑钱包 API 文档

## 接口概述

本接口用于解绑用户的钱包地址，只能解绑 `connectType` 为 `BIND` 类型的钱包连接。

## 接口详情

### HTTP 接口

**请求方式**: `POST`  
**请求路径**: `/passport/unbind-wallet`  
**Content-Type**: `application/json`

#### 请求参数

```json
{
    "passportId": "用户护照ID",
    "walletAddress": "要解绑的钱包地址"
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| passportId | String | 是 | 用户护照ID |
| walletAddress | String | 是 | 要解绑的钱包地址 |

#### 响应格式

```json
{
    "success": true,
    "code": "SUCCESS",
    "message": "操作成功",
    "data": true
}
```

#### 错误响应

```json
{
    "success": false,
    "code": "UNKNOWN_ERROR",
    "message": "passportId and walletAddress are required",
    "data": null
}
```

### Dubbo 接口

**接口**: `RemotePassportService.unbindWallet(UnbindWalletRequest request)`

#### 业务逻辑

1. 验证请求参数（passportId 和 walletAddress 不能为空）
2. 根据 passportId 和 walletAddress 查询 PassportConnect 记录
3. 检查记录是否存在
4. 验证 connectType 是否为 "BIND" 类型（只能解绑 BIND 类型的连接）
5. 更新记录状态为 "disable"
6. 设置 updatedAt 和 disconnectedAt 时间戳
7. 清除相关缓存

#### 返回值

- `true`: 解绑成功
- `false`: 解绑失败（记录不存在、connectType 不是 BIND 类型、或更新失败）

## 使用示例

### cURL 示例

```bash
curl -X POST http://localhost:8080/passport/unbind-wallet \
  -H "Content-Type: application/json" \
  -d '{
    "passportId": "1234567890",
    "walletAddress": "******************************************"
  }'
```

### Java 示例

```java
UnbindWalletRequest request = UnbindWalletRequest.builder()
    .passportId("1234567890")
    .walletAddress("******************************************")
    .build();

Response<Boolean> response = remotePassportService.unbindWallet(request);
if (response.isSuccess() && response.getData()) {
    System.out.println("钱包解绑成功");
} else {
    System.out.println("钱包解绑失败: " + response.getMessage());
}
```

## 注意事项

1. 只能解绑 `connectType` 为 "BIND" 类型的钱包连接
2. "KEY" 类型的连接不能通过此接口解绑
3. 解绑后，记录状态会变为 "disable"，但记录不会被删除
4. 解绑操作会清除相关的缓存数据
5. 如果记录不存在或 connectType 不正确，操作会失败并返回 false
