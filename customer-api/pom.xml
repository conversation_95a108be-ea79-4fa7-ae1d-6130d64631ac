<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.drex</groupId>
        <artifactId>drex-customer</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>customer-api</artifactId>
    <name>customer-api</name>

    <properties>
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <project.property.path>..</project.property.path>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kiki-core</artifactId>
        </dependency>
    </dependencies>
</project>
