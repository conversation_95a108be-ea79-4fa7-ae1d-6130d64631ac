package com.drex.customer.api.request;

import com.drex.customer.api.constants.WalletConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Request object for binding a wallet to a customer
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BindWalletRequest implements Serializable {
    /**
     * Passport ID
     */
    private String passportId;
    
    /**
     * Wallet address
     */
    private String walletAddress;
    
    /**
     * Wallet provider
     */
    private WalletConstant.WalletProviderEnum walletProvider;
    
    /**
     * Sub-connect provider if applicable
     */
    private WalletConstant.PlatformEnum subConnectProvider;
    
    /**
     * Type of wallet
     */
    private WalletConstant.WalletTypeEnum walletType;
    
    /**
     * Type of connection
     */
    private WalletConstant.ConnectTypeEnum connectType;

    /**
     * Additional account details in JSON format
     */
    private String accountDetail;
}
