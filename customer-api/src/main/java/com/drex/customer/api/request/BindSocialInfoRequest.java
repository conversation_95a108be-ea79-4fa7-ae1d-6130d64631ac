package com.drex.customer.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BindSocialInfoRequest implements Serializable {

    private String appId;
    private String customerId;
    private String platform;
    private String socialId;
    private String socialHandleName;
    private String socialEmail;
    private String socialProfileImage;
}