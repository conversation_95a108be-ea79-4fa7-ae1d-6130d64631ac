package com.drex.customer.api.response;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class PassportDTO implements java.io.Serializable{

    private String passportId;        // kseq generated
    private String passportChainId;
    private String contractAddress;
    private String address;
    private Long number;    // On-chain ID
    private String handleName;
    private String username;
    private String avatar;
    private String userId;
    private String email;
    private String phone;
    private String referralCode;
    private String kycLevel = "L0";
    private Date createdAt;
    private Date updatedAt;
    private String status;// pending, active

    private Boolean isNewUser = false;
    private Boolean isNewPassport = false;
    private Boolean isRedeemedSuccessfully = false;

    private List<PassportConnectDTO> passportConnects;
}
