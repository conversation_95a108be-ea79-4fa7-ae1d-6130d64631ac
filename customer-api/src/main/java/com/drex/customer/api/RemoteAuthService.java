package com.drex.customer.api;

import com.drex.customer.api.request.AuthLoginRequest;
import com.drex.customer.api.request.ThirdAuthRequest;
import com.drex.customer.api.response.CustomerDTO;
import com.drex.customer.api.response.PassportDTO;
import com.drex.customer.api.response.ThirdAuthDTO;
import com.drex.customer.api.response.ThirdBindingsDTO;
import com.kikitrade.framework.common.model.Response;

import java.util.List;

public interface RemoteAuthService {

    Response<PassportDTO> login(AuthLoginRequest request);

    Response<ThirdAuthDTO> thirdAuth(ThirdAuthRequest request);

    Response<List<ThirdBindingsDTO>> thirdBindings(String customerId);
}
