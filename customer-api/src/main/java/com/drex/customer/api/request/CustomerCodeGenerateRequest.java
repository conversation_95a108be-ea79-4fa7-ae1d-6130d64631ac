package com.drex.customer.api.request;

import com.drex.customer.api.constants.CustomerConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * Description:
 *
 * @Author: joseph.xiang
 * DateTime: 2024-6-22 17:02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CustomerCodeGenerateRequest implements Serializable {
    public String customerId;
    public CustomerConstants.CodeType type;
    public Integer level;
    public String source;
    public String scene;
    public Long expirationTime;
    public String code;
    // type 为interval limit的需要传以下字段
    public CustomerConstants.LimitCycle totalLimitCycle;
    public Integer totalLimitNumber;
    public CustomerConstants.LimitCycle userLimitCycle;
    public Integer userLimitNumber;
    public Boolean canReuse;

}
