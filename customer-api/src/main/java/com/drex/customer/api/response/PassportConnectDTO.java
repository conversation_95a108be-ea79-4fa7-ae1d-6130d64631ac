package com.drex.customer.api.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class PassportConnectDTO implements Serializable {

    private String identifier;     // range key
    private String passportId;      // partition key

    // Attributes
    private String connectProvider;
    private String subConnectProvider;
    private String accountDetail;
    private String walletAddress;
    private String status;         // pending, active, disable
    private String walletType;
    private String connectType;
    private Date connectedAt;
    private Date updatedAt;
    private Date disconnectedAt;
}
