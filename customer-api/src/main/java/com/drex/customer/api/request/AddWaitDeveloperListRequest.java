package com.drex.customer.api.request;

import lombok.Data;

import java.io.Serializable;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;

@Data
public class AddWaitDeveloperListRequest implements Serializable {

    private String projectName;

    private String projectCategory;

    private String projectCategoryOther;

    private String projectDescription;

    private String ecosystemAlignment;

    private String projectStage;

    private String projectWebsite;

    private String projectTwitter;

    private String projectCommunity;

    private String primaryContactName;

    private String contactEmail;

    private String contactTelegram;

    private String supportType;

    private String supportTypeOther;

    private String useOfFunds;

    private String teamBackground;

    private String supportingDocuments;

    private String additionalComments;
}
