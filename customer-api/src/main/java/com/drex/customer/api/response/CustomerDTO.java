package com.drex.customer.api.response;

import java.io.Serializable;

import lombok.Data;

/**
 * Customer data transfer object
 */
@Data
public class CustomerDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String thirdUserId;

    private String customerId;

    private String registerAddress;

    private String connectAddress;

    private String lowerConnectAddress;

    private String eoaAddress;

    private String smartAccountAddress;

    private String userId;

    private String username;

    private String userPicture;

    private String email;

    private Integer chainId;

    private String authProvider;

    private String allAccount;

    private String kycLevel = "L0";

    private Long registerTime;

    private String inviteCode;

    private Integer status;

    private Long created;

    private Boolean isNewUser = false;

    private String clientIp;

    private String deviceInfo;
}
