package com.drex.customer.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Request object for unbinding a wallet from a passport
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnbindWalletRequest implements Serializable {
    /**
     * Passport ID
     */
    private String passportId;
    
    /**
     * Wallet address to unbind
     */
    private String walletAddress;
}
