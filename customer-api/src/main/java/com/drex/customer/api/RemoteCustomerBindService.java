package com.drex.customer.api;

import com.drex.customer.api.request.BindSocialInfoRequest;
import com.drex.customer.api.request.CustomerBindQuery;
import com.drex.customer.api.response.CustomerBindDTO;

/**
 * <AUTHOR>
 * @date 2025/4/28 10:08
 * @description:
 */
public interface RemoteCustomerBindService {

    CustomerBindDTO findByCustomerId(String customerId, String socialPlatform);

    boolean bindSocial(BindSocialInfoRequest bindSocialInfoRequest);

    boolean unbindSocial(String customerId, String socialPlatform);

    boolean reservePrivacyAuth(String customerId, String socialPlatform, String privacyAuth);

    CustomerBindDTO queryBySocialInfo(CustomerBindQuery request);
}

