package com.drex.customer.api;

import com.drex.customer.api.request.BindWalletRequest;
import com.drex.customer.api.request.UnbindWalletRequest;
import com.drex.customer.api.response.PassportConnectDTO;
import com.drex.customer.api.response.PassportDTO;
import com.kikitrade.framework.common.model.Response;

import java.util.List;

public interface RemotePassportService {

    PassportDTO getPassportById(String passportId);

    Response<PassportDTO> getByHandleName(String handleName);

    Response<Boolean> updateHandleName(String passportId, String handleName);

    Response<Boolean> updateAvatar(String passportId, String avatar);

    Response<Boolean> bindWallet(BindWalletRequest request);

    Response<Boolean> unbindWallet(UnbindWalletRequest request);

    Response<Boolean> updateKycLevel(String customerId, String kycLevel);

    Response<List<PassportConnectDTO>> getPassportConnect(String passportId);
}
