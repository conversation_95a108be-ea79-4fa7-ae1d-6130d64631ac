package com.drex.customer.api;

import lombok.Getter;

@Getter
public enum ErrorCode {

    JWT_TOKEN_INVALID("20001", "jwt invalid"),
    INVITE_CODE_INVALID("20002", "invite code invalid"),

    AUTH_CODE_INVALID("20002", "auth code invalid"),
    AUTH_REPEAT("20003", "auth repeat"),
    WALLET_ALREADY_BIND("20004", "wallet already bind"),
    WALLET_REPEAT_BIND("20005", "wallet repeat bind"),

    CODE_ALREADY_EXIST("20004", "code already exist"),
    CODE_ALREADY_USED_BY_YOU("20005", "code already used by you"),
    CODE_USED_TOO_FREQUENTLY("20006", "code used too frequently"),
    CODE_NOT_EXIST("20007", "code not exist"),
    CODE_STATUS_NOT_EFFECTIVE("20008", "code status not effective"),
    CODE_SCENE_ERROR("20009", "code scene error"),
    CODE_IS_EXPIRED("20010", "code is expired"),
    CODE_USE_FAIL("20011", "code use fail"),
    GET_ANCHOR_ACCESS_TOKEN_FAIL("20012", "get anchor access token fail"),
    LOGIN_FAIL("20013", "login fail"),
    CODE_ALREADY_USED_BY_OTHER("20014", "code already used by other"),
    WALLET_REACHED_MAX_LIMIT("20015", "wallet reached the maximum limit"),
    CONNECTED_BY_ANOTHER_PASSPORT("20016", "connected by another passport"),

    UNKNOWN_ERROR("9999", "unknown error");

    private String code;
    private String message;

    ErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }
}
