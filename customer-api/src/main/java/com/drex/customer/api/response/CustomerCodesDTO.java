package com.drex.customer.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * Description:
 *
 * @Author: joseph.xiang
 * DateTime: 2024-5-22 17:02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerCodesDTO implements Serializable {
    /**
     * 兑换码
     * */
    private String code;
    /**
     * 拥有者
     * */
    private String customerId;
    /**
     * 来源
     * */
    private String source;
    /**
     * 类型 {@link Constants.CodeType}
     * */
    private String type;
    /**
     * 创建时间
     * */
    private Date createTime;
    /**
     * 状态 {@link CodeConstants.Status}
     * */
    private String status;
    /**
     * 不同类型code的排序值
     * */
    private Integer sort;

    /**
     * 同一类型下，不同等级的code  如NFT tier1 tier2 tier3 ...
     * */
    private Integer level;

    /**
     * 使用该兑换码的用户信息，只有一次性兑换码有以下属性，defaultInvite因为能多次使用，查看使用详情需要调用code详情接口
     * */
    private String userId;
    private String userWallet;

    private String ownerId;


    private String scene;
    /**
     * 使用该兑换码的次数
     * */
    private long inviteCount;

    /**
     * 总限制周期
     * */
    private String totalLimitCycle;

    /**
     * 总限制次数
     * */
    private Integer totalLimitNumber;
    /**
     * 总已使用次数
     * */
    private Integer totalLimitUsed;


    /**
     * 单个用户限制次数
     * */
    private Integer userLimitNumber;
    /**
     * 单个用户已使用次数
     * */
    private Integer userLimitUsed;

    /**
     * 修改已使用次数时间
     * */
    private Date modifyUsedTime;

}
