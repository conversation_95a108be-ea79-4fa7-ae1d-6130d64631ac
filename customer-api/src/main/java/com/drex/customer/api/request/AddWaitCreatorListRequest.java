package com.drex.customer.api.request;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class AddWaitCreatorListRequest implements Serializable {

    private String name;

    private String contactEmail;

    private String contactTelegram;

    private String contactDiscord;

    private String contactOther;

    private String socialsInstagram;

    private String socialsTwitter;

    private String socialsYoutube;

    private String socialsTiktok;

    private String socialsTelegram;

    private String socialsOther;

    private String contentTypes;

    private String contentTypesOther;

    private String interestReasons;

    private String interestReasonsOther;
}
