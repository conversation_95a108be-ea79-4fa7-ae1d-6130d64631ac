## RemoteAuthService
###login
#### Description
处理客户登录请求
#### Request
```json
{
  "walletAddress": "string",
  "clientIp": "string",
  "deviceInfo": "string"
}
#### Response
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "customerDTO": CustomerDTO
  }
}
```
#### process
1. 通过 walletAddress 查询客户信息, 通过customer表的connect_wallet或register_address字段查询
2. 如果客户不存在，创建客户信息
    ##### 1.通过walletAddress查询thirdweb用户信息
    ##### 2.创建客户信息，并保存到数据库
        ###### 特殊情况：
            1. 当客户信息中type等于wallet时，同时设定connect_wallet为walletAddress
    ##### 3.返回客户信息
3. 生成 token，并返回给客户端

##RemoteCustomerService
###bindWallet
#### Description
绑定钱包地址
#### Request
```json
{
  "customerId": "string",
  "walletAddress": "string"
}
#### Response
```json
{
  "code": 200,
  "message": "success",
  "data": true
}
```
#### process
1. 通过 customerId 查询客户信息
2. 如果客户不存在，返回错误
3. 如果客户存在，更新客户信息
    ##### 1.更新客户信息，设定connect_wallet为walletAddress
    ##### 2.返回true

