<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.drex</groupId>
    <artifactId>drex-customer</artifactId>
    <version>0.0.1-SNAPSHOT</version>
  </parent>

  <artifactId>customer-service</artifactId>
  <name>customer-service</name>

  <properties>
    <java.version>17</java.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <project.property.path>..</project.property.path>
    <springdoc.openapi.webmvc.version>2.0.2</springdoc.openapi.webmvc.version>
    <drex.customer.version>0.0.1-SNAPSHOT</drex.customer.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.drex</groupId>
      <artifactId>customer-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.drex</groupId>
      <artifactId>customer-dal</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>credentials-java</artifactId>
          <groupId>com.aliyun</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.kikitrade</groupId>
      <artifactId>kiki-dubbo-deployment-tag-aware-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.kikitrade</groupId>
      <artifactId>kiki-ons-spring-boot-starter</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>credentials-java</artifactId>
          <groupId>com.aliyun</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.dubbo</groupId>
      <artifactId>dubbo</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
      <version>1.5.5.Final</version>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct-processor</artifactId>
      <version>1.5.5.Final</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.twitter</groupId>
      <artifactId>twitter-api-java-sdk</artifactId>
      <version>2.0.3</version>
    </dependency>
    <dependency>
      <groupId>com.google.zxing</groupId>
      <artifactId>core</artifactId>
      <version>3.5.3</version>
    </dependency>
    <dependency>
      <groupId>com.aliyun.oss</groupId>
      <artifactId>aliyun-sdk-oss</artifactId>
      <version>3.18.1</version>
    </dependency>
    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>aliyun-java-sdk-core</artifactId>
      <version>4.5.10</version>
    </dependency>
    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>credentials-java</artifactId>
      <version>0.3.4</version>
    </dependency>
    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>tea-openapi</artifactId>
      <version>0.3.6</version>
    </dependency>
    <dependency>
      <groupId>com.google.zxing</groupId>
      <artifactId>core</artifactId>
      <version>3.5.3</version>
    </dependency>
    <dependency>
      <groupId>co.evg</groupId>
      <artifactId>evg-scaffold-event-client</artifactId>
      <version>1.0.0-SNAPSHOT</version>
      <type>pom</type>
    </dependency>
    <dependency>
      <groupId>co.evg</groupId>
      <artifactId>achievement-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
  </dependencies>
</project>
