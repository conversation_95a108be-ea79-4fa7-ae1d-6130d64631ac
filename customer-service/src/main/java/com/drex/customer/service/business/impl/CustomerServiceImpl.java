package com.drex.customer.service.business.impl;

import com.alibaba.fastjson2.JSON;
import com.drex.customer.api.request.AddWaitCreatorListRequest;
import com.drex.customer.api.request.AddWaitDeveloperListRequest;
import com.drex.customer.api.request.AddWaitListRequest;
import com.drex.customer.api.response.CustomerDTO;
import com.drex.customer.dal.tablestore.builder.CustomerBuilder;
import com.drex.customer.dal.tablestore.builder.CustomerCreatorWaitListBuilder;
import com.drex.customer.dal.tablestore.builder.CustomerDeveloperWaitListBuilder;
import com.drex.customer.dal.tablestore.builder.CustomerWaitListBuilder;
import com.drex.customer.dal.tablestore.model.Customer;
import com.drex.customer.dal.tablestore.model.CustomerWaitList;
import com.drex.customer.service.business.CustomerService;
import com.drex.customer.service.mapstruct.CustomerMapperStruct;
import com.kikitrade.framework.observability.metrics.business.KiKiMonitor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Slf4j
@Service("customerService")
public class CustomerServiceImpl implements CustomerService {

    private static final String CUSTOMER_CACHE_KEY = "customer:";
    private static final long CACHE_TTL_HOURS = 3;

    @Autowired
    private CustomerBuilder customerBuilder;
    @Autowired
    private CustomerMapperStruct customerMapperStruct;
    @Resource
    private CustomerWaitListBuilder customerWaitListBuilder;
    @Resource
    private CustomerDeveloperWaitListBuilder customerDeveloperWaitListBuilder;
    @Resource
    private CustomerCreatorWaitListBuilder customerCreatorWaitListBuilder;
    @Resource
    private KiKiMonitor kiKiMonitor;
    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public CustomerDTO getById(String customerId) {
        String cacheKey = CUSTOMER_CACHE_KEY + customerId;
        try {
            String cache = redisTemplate.opsForValue().get(cacheKey);
            if (StringUtils.isNotBlank(cache)) {
                return JSON.parseObject(cache, CustomerDTO.class);
            }
            // Cache miss - get from database
            Customer customer = customerBuilder.getById(customerId);
            CustomerDTO customerDTO = customerMapperStruct.toCustomerDTO(customer);

            // Cache the result
            if (customerDTO != null) {
                redisTemplate.opsForValue().set(
                    cacheKey,
                    JSON.toJSONString(customerDTO),
                    CACHE_TTL_HOURS,
                    TimeUnit.HOURS
                );
            }
            return customerDTO;
        } catch (Exception e) {
            log.error("Error while getting customer by ID with caching", e);
            return customerMapperStruct.toCustomerDTO(customerBuilder.getById(customerId));
        }
    }

    @Override
    public CustomerDTO getByRegisterAddress(String registerAddress) {
        return customerMapperStruct.toCustomerDTO(customerBuilder.getByRegisterAddress(registerAddress));
    }

    @Override
    public CustomerDTO getByInviteCode(String inviteCode) {
        return customerMapperStruct.toCustomerDTO(customerBuilder.getByInviteCode(inviteCode));
    }

    public Boolean updateLevel(String customerId, String kycLevel) {
        Boolean success = customerBuilder.updateLevel(customerId, kycLevel);
        if (success) {
            // Invalidate cache
            redisTemplate.delete(CUSTOMER_CACHE_KEY + customerId);
        }
        return success;
    }

    @Override
    public boolean addWaitList(AddWaitListRequest addWaitListRequest) {
        CustomerWaitList customerWaitList = customerMapperStruct.toCustomerWaitList(addWaitListRequest);
        return customerWaitListBuilder.insertOrUpdate(customerWaitList);
    }

    @Override
    public boolean updateConnectWallet(String customerId, String walletAddress) {
        try {
            Customer customer = customerBuilder.getById(customerId);
            if (customer == null) {
                return false;
            }
            customer.setConnectAddress(walletAddress);
            customer.setLowerConnectAddress(walletAddress.toLowerCase());
            boolean success = customerBuilder.update(customer);
            if (success) {
                // Invalidate cache
                redisTemplate.delete(CUSTOMER_CACHE_KEY + customerId);
            }
            return success;
        } catch (Exception e) {
            log.error("Failed to update connect_wallet for customer: " + customerId, e);
            return false;
        }
    }

    @Override
    public boolean addWaitList(AddWaitCreatorListRequest addWaitCreatorListRequest) {
        return customerCreatorWaitListBuilder.insertOrUpdate(customerMapperStruct.toCustomerCreatorWaitList(addWaitCreatorListRequest));
    }

    /**
     * 添加开发者预订阅列表
     *
     * @param addWaitDeveloperListRequest
     * @return
     */
    @Override
    public boolean addWaitList(AddWaitDeveloperListRequest addWaitDeveloperListRequest) {
        return customerDeveloperWaitListBuilder.insertOrUpdate(customerMapperStruct.toCustomerDeveloperWaitList(addWaitDeveloperListRequest));
    }
}
