package com.drex.customer.service.reference.impl.model.anchor;

/**
 * Anchor 系统相关静态变量
 */
public class AnchorConstants {

    public static String CACHE_KEY_ACCESS_TOKEN = "anchor:access_token:%s";

    public static String URL_ACCESS_TOKEN = "/oauth2/token";
    public static String URL_ASSETS_BADGE = "/s1/assets/badge";
    public static String URL_SERIES_DETAIL = "/s1/series/badge/%s";
    public static String URL_CHECK_EVENT = "/s1/check/event";


    public static String HEADER_AUTHORIZATION = "Authorization";
    public static String HEADER_CLIENT_ID = "client_id";
    public static String HEADER_CHAIN_ID = "chain_id";

    public static String PARAM_GRANT_TYPE = "grant_type";
    public static String PARAM_CLIENT_ID = "client_id";
    public static String PARAM_CLIENT_SECRET = "client_secret";

    public static String AUTHORIZATION_PREFIX = "Bearer ";

    public static String VALUE_GRANT_TYPE = "client_credentials";

    public static String SUCCESS_CODE = "0000";

}
