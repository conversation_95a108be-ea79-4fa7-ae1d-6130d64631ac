package com.drex.customer.service.business.impl;

import co.evg.scaffold.event.client.EventClient;
import co.evg.scaffold.event.client.EventDTO;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.drex.customer.api.constants.WalletConstant;
import com.drex.customer.api.request.CustomerBindQuery;
import com.drex.customer.api.response.PassportConnectDTO;
import com.drex.customer.dal.tablestore.builder.CustomerBindBuilder;
import com.drex.customer.dal.tablestore.builder.PassportBuilder;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.dal.tablestore.model.Passport;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.service.business.PassportService;
import com.drex.customer.service.business.SocialSyncService;
import com.drex.customer.service.business.ThirdWebService;
import com.drex.customer.service.config.CustomerProperties;
import com.kikitrade.framework.common.model.TokenPage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 社媒信息同步服务实现
 * <AUTHOR>
 */
@Service
@Slf4j
public class SocialSyncServiceImpl implements SocialSyncService {
    
    @Resource
    private PassportBuilder passportBuilder;
    
    @Resource
    private PassportService passportService;
    
    @Resource
    private ThirdWebService thirdWebService;
    
    @Resource
    private CustomerBindService customerBindService;

    @Resource
    private CustomerBindBuilder customerBindBuilder;

    @Resource
    private CustomerProperties customerProperties;

    @Resource
    private EventClient eventClient;

    
    @Override
    public SyncResult syncAllPassportSocialInfo() {
        log.info("开始同步所有passport的社媒信息到customerBind表");

        int totalProcessed = 0;
        int successCount = 0;
        int skipCount = 0;
        int errorCount = 0;
        List<String> failedPassportIds = new ArrayList<>();

        String nextToken = null;
        Integer batchSize = customerProperties.getSyncSocialBatchSize();
        Integer delaySeconds = customerProperties.getSyncSocialDelaySeconds();

        try {
            do {
                // 分页查询passport记录
                TokenPage<Passport> passportPage = passportBuilder.listAllPassport(batchSize, nextToken);
                List<Passport> passports = passportPage.getRows();

                if (!CollectionUtils.isEmpty(passports)) {
                    log.info("处理第{}批passport记录，数量: {}", totalProcessed / batchSize + 1, passports.size());

                    // 同步处理当前批次的passport记录
                    for (Passport passport : passports) {
                        ProcessResult result = processPassportSocialInfo(passport);

                        totalProcessed++;

                        switch (result.getStatus()) {
                            case SUCCESS:
                                successCount++;
                                break;
                            case SKIP:
                                skipCount++;
                                break;
                            case ERROR:
                                errorCount++;
                                failedPassportIds.add(passport.getPassportId());
                                break;
                        }

                        // 处理间延迟
                        if (delaySeconds > 0) {
                            try {
                                Thread.sleep(delaySeconds * 1000L);
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                                log.warn("处理间延迟被中断");
                                break;
                            }
                        }
                    }
                }

                nextToken = passportPage.getNextToken();

                // 批次间延迟
                if (StringUtils.isNotBlank(nextToken) && delaySeconds > 0) {
                    try {
                        Thread.sleep(delaySeconds * 1000L);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("批次间延迟被中断");
                        break;
                    }
                }

            } while (StringUtils.isNotBlank(nextToken));

            String message = String.format("同步完成，总计处理%d条记录，成功%d条，跳过%d条，失败%d条",
                    totalProcessed, successCount, skipCount, errorCount);
            log.info(message);

            return new SyncResult(totalProcessed, successCount, skipCount, errorCount, failedPassportIds, message);

        } catch (Exception e) {
            log.error("同步passport社媒信息时发生错误", e);
            return new SyncResult(totalProcessed, successCount, skipCount, errorCount, failedPassportIds,
                    "同步过程中发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 处理结果枚举
     */
    private enum ProcessStatus {
        SUCCESS, SKIP, ERROR
    }

    /**
     * 处理结果
     */
    private static class ProcessResult {
        private final ProcessStatus status;

        public ProcessResult(ProcessStatus status) {
            this.status = status;
        }

        public ProcessStatus getStatus() {
            return status;
        }
    }

    /**
     * 处理单个passport的社媒信息
     */
    private ProcessResult processPassportSocialInfo(Passport passport) {
        try {
            String passportId = passport.getPassportId();
            log.info("开始处理passport: {}", passportId);

            // 获取passport关联的钱包连接信息
            List<PassportConnectDTO> passportConnects = passportService.getPassportConnect(passportId);

            if (CollectionUtils.isEmpty(passportConnects)) {
                log.info("passport {} 没有关联的钱包连接信息，跳过", passportId);
                return new ProcessResult(ProcessStatus.SKIP);
            }

            boolean hasProcessed = false;

            // 遍历每个钱包连接，查询社媒信息
            for (PassportConnectDTO connect : passportConnects) {
                if (StringUtils.isBlank(connect.getWalletAddress()) ||
                    StringUtils.isBlank(connect.getSubConnectProvider())) {
                    continue;
                }

                // 跳过钱包类型的连接
                if ("wallet".equalsIgnoreCase(connect.getSubConnectProvider())) {
                    continue;
                }

                WalletConstant.PlatformEnum platformEnum = WalletConstant.PlatformEnum.getEnumByName(
                        connect.getSubConnectProvider());
                if (platformEnum == null) {
                    log.info("不支持的平台类型: {}", connect.getSubConnectProvider());
                    continue;
                }

                // 检查是否已经存在绑定记录
                CustomerBind existingBind = customerBindService.findByCustomerId(passportId, platformEnum.name());
                if (existingBind != null) {
                    if (WalletConstant.PlatformEnum.X == platformEnum) {
                        if (StringUtils.isNotBlank(existingBind.getSocialUserId())) {
                            log.debug("passport {} 平台 X 已存在有效绑定记录，跳过", passportId);
                            continue;
                        }
                        log.info("passport {} 平台 X 存在无效绑定记录，将尝试更新", passportId);
                    } else {
                        log.info("passport {} 平台 {} 已存在绑定记录，跳过", passportId, platformEnum.name());
                        continue;
                    }
                }

                // 调用ThirdWebService获取社媒信息
                ThirdWebService.ThirdWebUserAccount userAccount = thirdWebService.getThirdWebUserAccount(
                        connect.getWalletAddress(), platformEnum);

                if (userAccount != null) {
                    //校验是否被其他passport绑定了
                    CustomerBindQuery query = CustomerBindQuery.builder()
                            .socialPlatform(platformEnum.name())
                            .socialUserId(userAccount.getId())
                            .socialUserName(userAccount.getUsername())
                            .socialEmail(userAccount.getEmail())
                            .build();
                    CustomerBind bySocialInfo = customerBindService.findBySocialInfo(query);
                    if (bySocialInfo != null) {
                        //如果被别人绑定了，跳过
                        log.info("passport {} 平台 {} 已被另一个passport id: {}绑定, 社媒信息: {}，跳过", passportId, platformEnum.name(), bySocialInfo.getCustomerId(), JSON.toJSONString(userAccount));
                        continue;
                    }
                    // 创建CustomerBind记录
                    CustomerBind customerBind = createCustomerBind(passportId, platformEnum, userAccount);

                    // 插入到数据库
                    boolean inserted = customerBindService.insert(customerBind);
                    if (inserted) {
                        log.info("成功为passport {} 绑定社媒平台 {}", passportId, platformEnum.name());
                        //发送event消息，自动完成任务
                        sendEvent(connect);
                        hasProcessed = true;
                    } else {
                        log.warn("为passport {} 绑定社媒平台 {} 失败", passportId, platformEnum.name());
                    }
                } else {
                    log.info("passport {} 在ThirdWeb中未找到 {} 平台的社媒信息", passportId, platformEnum.name());
                }
            }

            return new ProcessResult(hasProcessed ? ProcessStatus.SUCCESS : ProcessStatus.SKIP);

        } catch (Exception e) {
            log.error("处理passport {} 的社媒信息时发生错误", passport.getPassportId(), e);
            return new ProcessResult(ProcessStatus.ERROR);
        }
    }
    
    /**
     * 创建CustomerBind对象
     */
    private CustomerBind createCustomerBind(String passportId, WalletConstant.PlatformEnum platform, 
                                          ThirdWebService.ThirdWebUserAccount userAccount) {
        CustomerBind customerBind = new CustomerBind();
        customerBind.setCustomerId(passportId);
        customerBind.setSocialPlatform(platform.name());
        customerBind.setSocialUserId(userAccount.getId());
        customerBind.setSocialHandleName(userAccount.getUsername());
        customerBind.setSocialEmail(userAccount.getEmail());
        customerBind.setCreated(System.currentTimeMillis());
        
        return customerBind;
    }

    @Override
    public void resetPrivacyAuth() {
        log.info("开始重置customer_bind表中所有记录的privacyAuth字段为null");

        int totalProcessed = 0;
        int successCount = 0;
        int errorCount = 0;
        List<String> failedRecords = new ArrayList<>();

        String nextToken = null;
        Integer batchSize = customerProperties.getSyncSocialBatchSize();
        Integer delaySeconds = customerProperties.getSyncSocialDelaySeconds();

        try {
            do {
                // 分页查询customer_bind记录
                TokenPage<CustomerBind> customerBindPage = customerBindBuilder.listAllCustomerBind(batchSize, nextToken);
                List<CustomerBind> customerBinds = customerBindPage.getRows();

                if (!CollectionUtils.isEmpty(customerBinds)) {
                    log.info("处理第{}批customer_bind记录，数量: {}", totalProcessed / batchSize + 1, customerBinds.size());

                    // 处理当前批次的记录
                    for (CustomerBind customerBind : customerBinds) {
                        try {
                            // 使用deletePrivacyAuth方法直接删除privacyAuth字段
                            boolean deleted = customerBindBuilder.deletePrivacyAuth(customerBind);
                            if (deleted) {
                                successCount++;
                                log.info("成功重置记录: customerId={}, socialPlatform={}",
                                        customerBind.getCustomerId(), customerBind.getSocialPlatform());
                            } else {
                                errorCount++;
                                String recordKey = customerBind.getCustomerId() + ":" + customerBind.getSocialPlatform();
                                failedRecords.add(recordKey);
                                log.warn("删除失败: customerId={}, socialPlatform={}",
                                        customerBind.getCustomerId(), customerBind.getSocialPlatform());
                            }

                            totalProcessed++;

                            // 处理间延迟
                            if (delaySeconds > 0) {
                                try {
                                    Thread.sleep(delaySeconds * 1000L);
                                } catch (InterruptedException e) {
                                    Thread.currentThread().interrupt();
                                    log.warn("处理间延迟被中断");
                                    break;
                                }
                            }

                        } catch (Exception e) {
                            errorCount++;
                            String recordKey = customerBind.getCustomerId() + ":" + customerBind.getSocialPlatform();
                            failedRecords.add(recordKey);
                            log.error("处理记录时发生错误: customerId={}, socialPlatform={}",
                                    customerBind.getCustomerId(), customerBind.getSocialPlatform(), e);
                        }
                    }
                }

                nextToken = customerBindPage.getNextToken();

                // 批次间延迟
                if (StringUtils.isNotBlank(nextToken) && delaySeconds > 0) {
                    try {
                        Thread.sleep(delaySeconds * 1000L);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("批次间延迟被中断");
                        break;
                    }
                }

            } while (StringUtils.isNotBlank(nextToken));

            String message = String.format("重置privacyAuth完成，总计处理%d条记录，成功%d条，失败%d条",
                    totalProcessed, successCount, errorCount);
            log.info(message);

            if (!failedRecords.isEmpty()) {
                log.warn("失败的记录: {}", failedRecords);
            }

        } catch (Exception e) {
            log.error("重置privacyAuth时发生错误", e);
            throw new RuntimeException("重置privacyAuth失败: " + e.getMessage(), e);
        }
    }

    private void sendEvent(PassportConnectDTO passportConnect) {
        JSONObject body = new JSONObject();
        body.put("saasId",  customerProperties.getSaasId());
        EventDTO event = EventDTO
                .builder()
                .name("connect_" + passportConnect.getSubConnectProvider())
                .customerId(passportConnect.getPassportId())
                .globalUid(passportConnect.getIdentifier() + "_" + passportConnect.getPassportId() + "_" + passportConnect.getSubConnectProvider())
                .time(System.currentTimeMillis())
                .body(body)
                .build();
        log.info("socialSync sendEvent, event:{}", event);
        eventClient.push(event);
    }
}
