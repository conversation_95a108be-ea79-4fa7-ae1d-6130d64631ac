package com.drex.customer.service.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
@Data
public class CustomerProperties {

    @Value("${customer.register.topic:T_CUSTOMER_EVENT_DREX}")
    private String customerRegisterTopic;

    @Value("${activity.event.topic:T_ACTIVITY_EVENT_DREX}")
    private String activityEventTopic;

    @Value("${customer.http.pool.size:150}")
    private Integer httpPoolSize;

    @Value("${customer.activity.event.delay.seconds:1}")
    private Integer activityEventDelaySeconds;

    @Value("${customer.thirdweb.secret:WlHddeLT5W6zwYd3EyFJ5Jz1TBiCsaBelX9vUuw32Nst6VdhiuFkgYS7_2_VXUIIhzYMAYZses6cwfMA1ehFcQ}")
    private String thirdWebSecretKey;

    @Value("${customer.thirdweb.client:58b31f236c6a997be4cc2084204ea665}")
    private String thirdWebClientId;

    @Value("${qr.url:https://drex-dev-new.oss-ap-southeast-1.aliyuncs.com/drex/trex/rexy_qr.png}")
    private String qrUrl;

    @Value("${customer.anchor.project-id:trex}")
    private String anchorProjectId;

    @Value("${customer.anchor.chain-id:1962}")
    private String anchorChainId;

    @Value("${customer.anchor.host:https://anchordev.dipbit.xyz}")
    private String anchorHost;

    @Value("${customer.anchor.client-id:trex}")
    private String anchorClientId;

    @Value("${customer.anchor.client-secret:IC69PNKFS5802888}")
    private String anchorClientSecret;

    @Value("${customer.sync.social.delay.seconds:2}")
    private Integer syncSocialDelaySeconds;

    @Value("${customer.sync.social.batch.size:50}")
    private Integer syncSocialBatchSize;

    @Value("${customer.social.platform.skip.list:Apple,Line}")
    private List<String> socialPlatformSkipList;

    private String saasId;

    @Value("${customer.wallet.bind.max.count:10}")
    // 最大绑定钱包数量
    private Integer walletBindMaxCount;

    @Value("${customer.wait.list.effective.time:1754006400000}")
    private Long customerWaitListEffectiveTime;
}
