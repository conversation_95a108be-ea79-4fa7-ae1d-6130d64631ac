package com.drex.customer.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.drex.customer.api.ErrorCode;
import com.drex.customer.dal.tablestore.constant.BusinessMonitor;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.service.business.AuthService;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.service.socialPlatform.AccessToken;
import com.drex.customer.service.socialPlatform.model.OpenAuthRequest;
import com.drex.customer.service.socialPlatform.model.SocialUserInfo;
import com.drex.customer.service.socialPlatform.model.Token;
import com.drex.customer.service.socialPlatform.service.SocialPlatformService;
import com.drex.customer.service.socialPlatform.service.SocialPlatformServiceFactory;
import com.drex.model.CustomerException;
import com.kikitrade.framework.observability.metrics.business.KiKiMonitor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Service;

/**
 * 认证服务实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuthServiceImpl implements AuthService {

    @Resource
    private OkHttpClient okHttpClient;
    @Resource
    private KiKiMonitor kiKiMonitor;
    @Resource
    private CustomerBindService customerBindService;
    @Resource
    private SocialPlatformServiceFactory platformServiceFactory;

    @Override
    public Token auth(String platform, String code, String customerId) throws CustomerException {
        log.info("[AuthServiceImpl] auth platform: {} code: {} customerId: {}", platform, code, customerId);
        // 检查用户是否已绑定该平台
        CustomerBind customerBind = customerBindService.findByCustomerId(customerId, platform);
        if (customerBind != null && customerBind.getSocialUserId() != null) {
            log.info("[auth] Platform already bound: {}, {}", platform, customerId);
            Token token = new Token();
            token.setSocialUserId(customerBind.getSocialUserId());
            token.setSocialHandleName(customerBind.getSocialHandleName());
            return token;
        }

        // 获取对应平台的服务
        SocialPlatformService platformService = platformServiceFactory.getService(platform);
        if (platformService == null) {
            log.error("[auth] Platform not supported: {}", platform);
            throw new CustomerException(ErrorCode.UNKNOWN_ERROR);
        }

        // 构建授权请求
        OpenAuthRequest authRequest = platformService.buildAuthRequest(code);
        kiKiMonitor.monitor(BusinessMonitor.THIRD_AUTH,
                new String[]{"platform", platform, "code", "request"});

        // 发送授权请求获取访问令牌
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create(mediaType, authRequest.getQuery());
        Request request = new Request.Builder()
                .url(authRequest.getUrl())
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Authorization", "Basic " + authRequest.getBasic())
                .build();

        try {
            Response response = okHttpClient.newCall(request).execute();
            String responseBody = response.body().string();
            JSONObject jsonObject = JSON.parseObject(responseBody);
            log.info("[auth] Auth result: {}, {}, {}", authRequest, jsonObject, customerId);

            if (!response.isSuccessful() || !jsonObject.containsKey("access_token")) {
                kiKiMonitor.monitor(BusinessMonitor.THIRD_AUTH,
                        new String[]{"platform", platform, "code", "token_invalid"});
                throw new CustomerException(ErrorCode.AUTH_CODE_INVALID);
            }

            // 创建令牌对象
            Token token = new Token();
            token.setAccessToken(jsonObject.getString("access_token"));
            token.setRefreshToken(jsonObject.getString("refresh_token"));
            token.setSocialUserId(jsonObject.getString("user_id"));

            // 获取用户信息
            AccessToken accessToken = AccessToken.builder()
                    .accessToken(token.getAccessToken())
                    .refreshToken(token.getRefreshToken())
                    .userId(token.getSocialUserId())
                    .build();

            SocialUserInfo userInfo = platformService.getCurrentUser(accessToken);
            if (userInfo == null) {
                kiKiMonitor.monitor(BusinessMonitor.THIRD_AUTH,
                        new String[]{"platform", platform, "code", "user_not_found"});
                throw new CustomerException(ErrorCode.AUTH_CODE_INVALID);
            }

            // 使用平台特定的验证逻辑检查该社交账号是否已被其他用户绑定
            customerBind = platformService.validateUserBinding(userInfo);
            if (customerBind != null) {
                log.info("[auth] Social account already bound by another user: platform={}, userId={}, username={}, email={}",
                        platform, userInfo.getUserId(), userInfo.getUsername(), userInfo.getEmail());
                kiKiMonitor.monitor(BusinessMonitor.THIRD_AUTH,
                        new String[]{"platform", platform, "code", "auth_repeat"});
                throw new CustomerException(ErrorCode.AUTH_REPEAT);
            }

            // 更新令牌信息
            token.setSocialUserId(userInfo.getUserId());
            token.setSocialHandleName(userInfo.getUsername());

            // 创建绑定记录
            customerBind = customerBindService.findByCustomerId(platform, userInfo.getUserId());
            if (customerBind == null) {
                customerBind = new CustomerBind();
            }
            customerBind.setCustomerId(customerId);
            customerBind.setSocialPlatform(platform);
            customerBind.setSocialUserId(token.getSocialUserId());
            customerBind.setSocialHandleName(token.getSocialHandleName());
            customerBind.setSocialProfileImage(userInfo.getProfileImageUrl());
            customerBind.setSocialEmail(userInfo.getEmail());
            customerBind.setPrivacyAuth(false);

            // 保存绑定记录
            boolean res = customerBindService.insert(customerBind);
            log.info("[auth] Bind social: {}, {}", authRequest, res);

            if (!res) {
                throw new CustomerException(ErrorCode.AUTH_CODE_INVALID);
            }

            return token;
        } catch (CustomerException ex) {
            log.error("[auth] Auth error (customer exception): {}", authRequest, ex);
            throw ex;
        } catch (Exception e) {
            log.error("[auth] Auth error: {}", authRequest, e);
            throw new CustomerException(ErrorCode.AUTH_CODE_INVALID);
        }
    }
}
