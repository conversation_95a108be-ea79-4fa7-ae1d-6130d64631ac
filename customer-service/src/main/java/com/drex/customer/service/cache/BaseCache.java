package com.drex.customer.service.cache;

import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * Base Cache for SDK Client
 *
 * <AUTHOR>
 * @create 2021/6/8 11:30 上午
 * @modify
 */
@Slf4j
@Component
public class BaseCache<K, V> {

    public static final long DEFAULT_EXPIRE_HOUR = 3;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    protected void init(RedisTemplate<String, String> redisTemplate){
        this.redisTemplate = redisTemplate;
    }

    public V get(K k) {
        return null;
    }

    public V getUnchecked(String key) {
        V v = loadEntry(key);
        if(v != null) {
            redisTemplate.opsForValue().set(prefix()+":"+key, JSON.toJSONString(v), expireSeconds(), TimeUnit.SECONDS);
        }
        return v;
    }

    public V loadEntry(String s){
        return null;
    }

    public boolean delete(String key) {
        redisTemplate.delete(prefix()+":"+key);
        return false;
    }

    /**
     * Expired seconds
     *
     * @return
     */
    protected long expireSeconds() {
        return TimeUnit.HOURS.toSeconds(DEFAULT_EXPIRE_HOUR);
    }

    public String prefix() {
        return "";
    }
}
