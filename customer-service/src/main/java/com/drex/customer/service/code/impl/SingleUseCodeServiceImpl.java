package com.drex.customer.service.code.impl;

import com.alibaba.fastjson.JSONObject;
import com.drex.customer.api.ErrorCode;
import com.drex.customer.api.constants.CustomerConstants;
import com.drex.customer.api.request.CustomerCodeGenerateRequest;
import com.drex.customer.api.response.PassportDTO;
import com.drex.customer.dal.tablestore.builder.CustomerCodeTransactionBuilder;
import com.drex.customer.dal.tablestore.builder.impl.CustomerCodesBuilderImpl;
import com.drex.customer.dal.tablestore.model.CustomerCodeTransaction;
import com.drex.customer.dal.tablestore.model.CustomerCodes;
import com.drex.customer.service.code.AbstractCustomerCodeService;
import com.drex.model.CustomerException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.drex.customer.api.constants.CustomerConstants.SINGLE_USE;

/**
 * @ClassName CustomerInviteServiceImpl
 * @Description 单次使用的邀请码，使用后即失效
 * <AUTHOR>
 * @Date 2024/5/20 19:13
 **/
@Service(SINGLE_USE)
@Slf4j
public class SingleUseCodeServiceImpl extends AbstractCustomerCodeService {

    @Resource
    CustomerCodesBuilderImpl customerCodesBuilder;

    @Resource
    CustomerCodeTransactionBuilder customerCodeTransactionBuilder;

    @Override
    public String generateCode(CustomerCodeGenerateRequest request) throws CustomerException {
        String code = StringUtils.isNotBlank(request.getCode()) ? request.getCode() : defaultGenerateCode();
        Date expirationTime = Objects.isNull(request.getExpirationTime()) ? null : new Date(request.getExpirationTime());
        if (!customerCodesBuilder.putRow(
                new CustomerCodes()
                        .setCode(code)
                        .setCustomerId(request.getCustomerId())
                        .setCreateTime(new Date())
                        .setSource(request.getSource())
                        .setUseCount(0)
                        .setLevel(1)
                        .setExpirationTime(expirationTime)
                        .setCanReuse(false)
                        .setScene(request.getScene())
                        .setStatus(CustomerConstants.Status.EFFECTIVE.getName())
                        .setType(CustomerConstants.CodeType.SINGLE_USE.getName())
                        .setSort(CustomerConstants.CodeType.SINGLE_USE.getCode())
        )) {
            throw new CustomerException(ErrorCode.CODE_ALREADY_EXIST);
        }
        return code;
    }

    @Override
    public CustomerCodes checkCode(CustomerCodes codesDO, String[] scene, String customerId) throws CustomerException {
        CustomerCodes customerCodes = defaultCheck(codesDO, new String[]{codesDO.getScene()}, customerId);
        if(customerCodes != null && customerId != null) {
            List<CustomerCodeTransaction> useList = customerCodeTransactionBuilder.getUseList(customerId, codesDO.getScene());
            if(!CollectionUtils.isEmpty(useList)){
                throw new CustomerException(ErrorCode.CODE_SCENE_ERROR);
            }
        }
        return customerCodes;
    }

    @Override
    public CustomerCodeTransaction useCode(PassportDTO passportDTO, CustomerCodes codesDO, String[] scene) throws CustomerException {
        log.info("SingleUseCodeServiceImpl useCode start customerDO:{} codeDO:{} scene:{}", passportDTO, JSONObject.toJSONString(codesDO), scene);
        codesDO = checkCode(codesDO, scene, passportDTO.getPassportId());
        codesDO.setStatus(CustomerConstants.Status.WRITTEN_OFF.getName());
        if (customerCodesBuilder.updateStatusOff(codesDO)) {
            //券码核销成功后记录流水
            CustomerCodeTransaction customerCodeTransactionDO = new CustomerCodeTransaction();
            customerCodeTransactionDO
                    .setCode(codesDO.getCode())
                    .setType(codesDO.getType())
                    .setLevel(codesDO.getLevel())
                    .setUseTime(new Date())
                    .setUserId(passportDTO.getPassportId())
                    .setScene(codesDO.getScene())
                    .setOwnerId(codesDO.getCustomerId());
            customerCodeTransactionBuilder.create(customerCodeTransactionDO);

            return customerCodeTransactionDO;
        };
        log.error("SingleUseCodeServiceImpl useCode fail, customerDO:{} code:{} scene:{}", passportDTO, JSONObject.toJSONString(codesDO), scene);
        throw new CustomerException(ErrorCode.CODE_USE_FAIL);
    }

}
