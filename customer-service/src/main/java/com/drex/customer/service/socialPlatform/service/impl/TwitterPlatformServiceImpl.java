package com.drex.customer.service.socialPlatform.service.impl;

import com.drex.customer.api.request.CustomerBindQuery;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.service.socialPlatform.AccessToken;
import com.drex.customer.service.socialPlatform.config.SocialPlatformProperties;
import com.drex.customer.api.constants.WalletConstant;
import com.drex.customer.service.socialPlatform.model.OpenAuthRequest;
import com.drex.customer.service.socialPlatform.model.SocialUserInfo;
import com.drex.customer.service.socialPlatform.service.SocialPlatformService;
import com.drex.model.CustomerException;
import com.twitter.clientlib.TwitterCredentialsOAuth2;
import com.twitter.clientlib.api.TwitterApi;
import com.twitter.clientlib.model.User;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.Set;

/**
 * Twitter平台服务实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TwitterPlatformServiceImpl implements SocialPlatformService {

    private static final String X_AUTH_URL = "https://api.twitter.com/2/oauth2/token";

    @Autowired
    private SocialPlatformProperties socialPlatformProperties;

    @Resource
    private CustomerBindService customerBindService;

    @Override
    public String getPlatformType() {
        return WalletConstant.PlatformEnum.X.name();
    }

    @Override
    public String generateAuthUrl() {
        return String.format("%s&client_id=%s&redirect_uri=%s",
                socialPlatformProperties.getTwitter().getAuthScore(),
                socialPlatformProperties.getTwitter().getClientId(),
                socialPlatformProperties.getTwitter().getPcRedirectUri());
    }

    @Override
    public OpenAuthRequest buildAuthRequest(String code) {
        return OpenAuthRequest.builder()
                .url(X_AUTH_URL)
                .clientId(socialPlatformProperties.getTwitter().getClientId())
                .query(String.format("code=%s&grant_type=authorization_code&redirect_uri=%s&code_verifier=trex",
                        code,
                        socialPlatformProperties.getTwitter().getPcRedirectUri()))
                .basic(Base64.getEncoder().encodeToString(
                        String.format("%s:%s",
                                socialPlatformProperties.getTwitter().getClientId(),
                                socialPlatformProperties.getTwitter().getClientSecret()).getBytes()))
                .build();
    }

    @Override
    public SocialUserInfo getCurrentUser(AccessToken accessToken) throws CustomerException {
        try {
            // 使用Twitter API获取用户信息
            TwitterApi twitterApi = new TwitterApi(new TwitterCredentialsOAuth2(
                    socialPlatformProperties.getTwitter().getClientId(), socialPlatformProperties.getTwitter().getClientSecret(),
                    accessToken.getAccessToken(), accessToken.getRefreshToken(),
                    accessToken.getRefreshToken() != null
            ));
            User user = twitterApi
                    .users()
                    .findMyUser()
                    .userFields(Set.of("created_at"))
                    .execute()
                    .getData();
            if (user == null) {
                log.error("Failed to get Twitter user info");
                return null;
            }

            return convertToSocialUserInfo(user);
        } catch (Exception e) {
            log.error("Error getting Twitter user info", e);
            return null;
        }
    }

    @Override
    public CustomerBind validateUserBinding(SocialUserInfo userInfo) {
        if (userInfo == null) {
            return null;
        }

        // 构建查询参数，X平台优先使用username进行验证
        CustomerBindQuery query = CustomerBindQuery.builder()
                .socialPlatform(getPlatformType())
                .socialUserId(userInfo.getUserId())
                .socialUserName(userInfo.getUsername())
                .build();

        log.info("[TwitterPlatformService] Validating user binding with query: platform={}, userId={}, username={}",
            query.getSocialPlatform(), query.getSocialUserId(), query.getSocialUserName());

        CustomerBind existingBind = customerBindService.findBySocialInfo(query);
        if (existingBind != null) {
            log.info("[TwitterPlatformService] Found existing binding: {}", existingBind);
        }

        return existingBind;
    }

    /**
     * 将Twitter用户信息转换为统一的社交用户信息模型
     */
    private SocialUserInfo convertToSocialUserInfo(User user) {

        return SocialUserInfo.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .profileImageUrl(null)
                .createdAt(user.getCreatedAt() != null ? user.getCreatedAt().toString() : null)
                .build();
    }
}
