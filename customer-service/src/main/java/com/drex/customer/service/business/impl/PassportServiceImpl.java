package com.drex.customer.service.business.impl;

import co.evg.scaffold.event.client.EventClient;
import co.evg.scaffold.event.client.EventDTO;
import com.alibaba.fastjson.JSONObject;
import com.drex.customer.api.constants.CustomerConstants;
import com.drex.customer.api.request.AuthLoginRequest;
import com.drex.customer.api.request.BindWalletRequest;
import com.drex.customer.api.request.UnbindWalletRequest;
import com.drex.customer.api.response.PassportConnectDTO;
import com.drex.customer.api.response.PassportDTO;
import com.drex.customer.dal.tablestore.builder.CustomerWaitListBuilder;
import com.drex.customer.dal.tablestore.builder.PassportBuilder;
import com.drex.customer.dal.tablestore.builder.PassportConnectBuilder;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.dal.tablestore.model.CustomerWaitList;
import com.drex.customer.dal.tablestore.model.Passport;
import com.drex.customer.dal.tablestore.model.PassportConnect;
import com.drex.customer.service.business.PassportService;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.service.business.InviteService;
import com.drex.customer.service.business.ThirdWebService;
import com.drex.customer.service.config.CustomerProperties;
import com.drex.customer.service.mapstruct.PassportMapperStruct;
import com.drex.customer.api.constants.WalletConstant;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.tuple.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class PassportServiceImpl implements PassportService {

    @Resource
    private PassportBuilder passportBuilder;
    @Resource
    private PassportConnectBuilder passportConnectBuilder;
    @Resource
    private CustomerWaitListBuilder customerWaitListBuilder;
    @Resource
    private PassportMapperStruct passportMapperStruct;
    @Resource
    private InviteService inviteService;
    @Resource
    private ThirdWebService thirdWebService;
    @Resource
    private EventClient eventClient;
    @Resource
    private CustomerProperties customerProperties;
    @Autowired
    private CustomerBindService customerBindService;

    @Override
    public PassportDTO getPassportById(String passportId) {
        Passport passport = passportBuilder.getByPassportId(passportId);
        List<PassportConnect> passportConnects = passportConnectBuilder.getByPassportId(passportId);
        passportConnects.removeIf(connect -> !WalletConstant.PassportConnectStatus.ACTIVE.getCode().equals(connect.getStatus()));
        PassportDTO passportDTO = passportMapperStruct.toPassportDTO(passport);
        if(!CollectionUtils.isEmpty(passportConnects)){
            passportDTO.setAddress(passportConnects.get(0).getIdentifier());
        }
        return passportDTO;
    }

    @Override
    public PassportDTO getPassportByWalletAddress(String walletAddress, WalletConstant.PlatformEnum subConnectProvider) {
        if (StringUtils.isBlank(walletAddress) || Objects.isNull(subConnectProvider)) {
            log.error("getPassportByWalletAddress param required, walletAddress:{}, subConnectProvider:{}", walletAddress, subConnectProvider);
            return null;
        }
        PassportConnect identifier = passportConnectBuilder.getByIdentifier(walletAddress, subConnectProvider.getCode());
        if(identifier == null || !WalletConstant.ConnectTypeEnum.KEY.getCode().equalsIgnoreCase(identifier.getConnectType()) ||
                !WalletConstant.PassportConnectStatus.ACTIVE.getCode().equalsIgnoreCase(identifier.getStatus())){
            log.error("getPassportByWalletAddress not found, walletAddress:{}, subConnectProvider:{},) passportConnect:{}", walletAddress, subConnectProvider, identifier);
            return null;
        }
        Passport passport = passportBuilder.getByPassportId(identifier.getPassportId());
        PassportDTO passportDTO = passportMapperStruct.toPassportDTO(passport);
        passportDTO.setAddress(identifier.getIdentifier());
        return passportDTO;
    }

    @Override
    public PassportDTO createPassport(AuthLoginRequest request) {
        try{
            PassportConnect passportConnect = new PassportConnect();
            passportConnect.setWalletAddress(request.getWalletAddress());
            passportConnect.setStatus(WalletConstant.PassportConnectStatus.ACTIVE.getCode());
            passportConnect.setConnectType(WalletConstant.ConnectTypeEnum.KEY.getCode());
            passportConnect.setConnectedAt(new Date());
            passportConnect.setUpdatedAt(new Date());
            passportConnect.setDisconnectedAt(null);

            Passport passport= new Passport();
            passport.setReferralCode(inviteService.generateInviteCode());
            passport.setCreatedAt(new Date());

            if (request.getSubConnectProvider() != null && WalletConstant.PlatformEnum.Wallet != request.getSubConnectProvider()) {
                ThirdWebService.ThirdWebUserAccount thirdWebUserAccount = thirdWebService.getThirdWebUserAccount(request.getWalletAddress(), request.getSubConnectProvider());
                if (thirdWebUserAccount == null) {
                    log.error("thirdweb user not found, walletAddress:{}, subConnectProvider:{}", request.getWalletAddress(), request.getSubConnectProvider());
                    return null;
                }
                passportConnect.setIdentifier(thirdWebUserAccount.getAddress());
                passportConnect.setWalletAddress(thirdWebUserAccount.getAddress());
                passportConnect.setConnectProvider(WalletConstant.WalletProviderEnum.ThirdWeb.getCode());
                passportConnect.setSubConnectProvider(thirdWebUserAccount.getType());
                passportConnect.setWalletType(WalletConstant.WalletTypeEnum.SOCIAL.getCode());
                passportConnect.setAccountDetail(thirdWebUserAccount.getDetails());

                passport.setUserId(thirdWebUserAccount.getId());
                passport.setUsername(thirdWebUserAccount.getUsername());
                passport.setEmail(thirdWebUserAccount.getEmail());
            } else {
                passportConnect.setIdentifier(request.getWalletAddress());
                passportConnect.setWalletAddress(request.getWalletAddress());
                passportConnect.setConnectProvider(WalletConstant.WalletProviderEnum.Wallet.getCode());
                passportConnect.setSubConnectProvider(WalletConstant.PlatformEnum.Wallet.getCode());
                passportConnect.setWalletType(WalletConstant.WalletTypeEnum.EVM.getCode());
            }
            passportBuilder.save(passport);
            passportConnect.setPassportId(passport.getPassportId());
            passportConnectBuilder.save(passportConnect);
            sendConnectEvent(passportConnect);
            if (!WalletConstant.PlatformEnum.Wallet.getCode().equalsIgnoreCase(passportConnect.getSubConnectProvider())) {
                CustomerBind customerBind = new CustomerBind();
                customerBind.setCustomerId(passport.getPassportId());
                customerBind.setSocialPlatform(WalletConstant.PlatformEnum.getEnumByCode(passportConnect.getSubConnectProvider()).name());
                customerBind.setSocialUserId(passport.getUserId());
                customerBind.setSocialHandleName(passport.getUsername());
                customerBind.setSocialEmail(passport.getEmail());
                customerBind.setCreated(System.currentTimeMillis());
                customerBindService.insert(customerBind);
            }
            PassportDTO passportDTO = passportMapperStruct.toPassportDTO(passport);
            passportDTO.setAddress(passportConnect.getIdentifier());
            return passportDTO;
        }catch (Exception ex){
            log.error("register error", ex);
            return null;
        }
    }

    @Override
    public PassportDTO getByHandleName(String handleName) {
        List<Passport> passport = passportBuilder.getByHandleName(handleName);
        if (CollectionUtils.isEmpty(passport)) {
            return null;
        }
        return passportMapperStruct.toPassportDTO(passport.get(0));
    }

    @Override
    public PassportDTO getByInviteCode(String inviteCode) {
        Passport passport = passportBuilder.getByInviteCode(inviteCode);
        if(passport == null){
            return null;
        }
        return passportMapperStruct.toPassportDTO(passport);
    }

    @Override
    public Boolean updateHandleName(String passportId, String handleName) {
        Passport passport = passportBuilder.getByPassportId(passportId);
        if (passport != null && passport.getHandleName() == null && handleName != null) {
            passport.setHandleName(handleName);
            passport.setLowerHandleName(handleName.toLowerCase());
            return passportBuilder.updatePassport(passport);
        }
        return false;
    }

    @Override
    public Pair<Boolean, String> updateOnChainInfo(String walletAddress, String passportChainId, String contractAddress) {
        List<PassportConnectDTO> passportConnectByAddress = getPassportConnectByAddress(walletAddress);
        if (CollectionUtils.isEmpty(passportConnectByAddress)) {
            log.info("no passport connect found, walletAddress:{}", walletAddress);
            AuthLoginRequest request = new AuthLoginRequest();
            request.setWalletAddress(walletAddress);
            request.setSubConnectProvider(WalletConstant.PlatformEnum.Wallet);
            PassportDTO passport = createPassport(request);
            if (passport != null) {
                log.info("new passport created, passportId:{}, passportChainId:{}, contractAddress:{}", passport.getPassportId(), passportChainId, contractAddress);
                Passport byPassportId = passportBuilder.getByPassportId(passport.getPassportId());
                byPassportId.setPassportChainId(passportChainId);
                byPassportId.setContractAddress(contractAddress);
                boolean success = passportBuilder.updatePassport(byPassportId);
                return Pair.of(success, byPassportId.getPassportId());
            }
        } else {
            PassportConnectDTO passportConnectDTO = passportConnectByAddress.stream().filter(connect ->
                    WalletConstant.ConnectTypeEnum.KEY.getCode().equalsIgnoreCase(connect.getConnectType())
            ).findFirst().orElse(null);
            if (passportConnectDTO != null) {
                PassportDTO passportDTO = getPassportByWalletAddress(walletAddress, WalletConstant.PlatformEnum.getEnumByCode(passportConnectDTO.getSubConnectProvider()));
                log.info("updateOnChainInfo, passportId:{}, passportChainId:{}, contractAddress:{}", passportDTO.getPassportId(), passportChainId, contractAddress);
                Passport byPassportId = passportBuilder.getByPassportId(passportDTO.getPassportId());
                byPassportId.setPassportChainId(passportChainId);
                byPassportId.setContractAddress(contractAddress);
                boolean success = passportBuilder.updatePassport(byPassportId);
                return Pair.of(success, passportDTO.getPassportId());
            } else {
                log.error("updateOnChainInfo, illegal data, bind wallet address cannot create passport, walletAddress:{}", walletAddress);
                return Pair.of(false, null);
            }
        }
        return Pair.of(false, null);
    }

    @Override
    public Boolean updateAvatar(String passportId, String avatar) {
        Passport passport = passportBuilder.getByPassportId(passportId);
        passport.setAvatar(avatar);
        return passportBuilder.updatePassport(passport);
    }

    @Override
    public Boolean bindWalletAddress(BindWalletRequest request) {
        PassportConnect passportConnect = new PassportConnect();
        passportConnect.setPassportId(request.getPassportId());
        passportConnect.setIdentifier(request.getWalletAddress());
        passportConnect.setWalletAddress(request.getWalletAddress());
        passportConnect.setConnectProvider(request.getWalletProvider().getCode());
        passportConnect.setSubConnectProvider(request.getSubConnectProvider().getCode());
        passportConnect.setAccountDetail(request.getAccountDetail());
        passportConnect.setWalletType(request.getWalletType().getCode());
        passportConnect.setConnectType(request.getConnectType().getCode());
        passportConnect.setStatus(WalletConstant.PassportConnectStatus.ACTIVE.getCode());
        passportConnect.setConnectedAt(new Date());
        passportConnect.setUpdatedAt(new Date());
        passportConnect.setDisconnectedAt(null);
        Boolean save = passportConnectBuilder.save(passportConnect);
        if(save){
            sendConnectEvent(passportConnect);
            checkWaitListBadges(passportConnect);
        }
        return save;
    }

    public void checkWaitListBadges(PassportConnect passportConnect){
        CustomerWaitList waitList =
                customerWaitListBuilder.getWaitListByAddressOrEmail(null ,passportConnect.getWalletAddress(), 0l, customerProperties.getCustomerWaitListEffectiveTime());
        if (waitList != null) {
            log.info("find wait list customer {}, address {}", passportConnect.getPassportId(), passportConnect.getWalletAddress());
            sendWaitListEvent(passportConnect.getPassportId(), CustomerConstants.SCENE_WEBSITE_WAITLIST, CustomerConstants.SERIES_TREX_JOURNEY);
        }
    }

    @Override
    public Boolean unbindWalletAddress(UnbindWalletRequest request) {
        try {
            // 根据passportId和walletAddress查询PassportConnect记录
            PassportConnect passportConnect = passportConnectBuilder.getByPassportIdAndWalletAddress(
                    request.getPassportId(), request.getWalletAddress());

            if (passportConnect == null) {
                log.warn("PassportConnect not found for passportId: {} and walletAddress: {}",
                        request.getPassportId(), request.getWalletAddress());
                return false;
            }

            // 检查connectType是否为BIND类型
            if (!WalletConstant.ConnectTypeEnum.BIND.getCode().equalsIgnoreCase(passportConnect.getConnectType())
                || !WalletConstant.PassportConnectStatus.ACTIVE.getCode().equalsIgnoreCase(passportConnect.getStatus())) {
                log.warn("Cannot unbind wallet with connectType: {} status: {} for passportId: {} and walletAddress: {}",
                        passportConnect.getConnectType(), passportConnect.getStatus(), request.getPassportId(), request.getWalletAddress());
                return false;
            }

            // 更新状态为disable
            passportConnect.setStatus(WalletConstant.PassportConnectStatus.DISABLED.getCode());
            passportConnect.setUpdatedAt(new Date());
            passportConnect.setDisconnectedAt(new Date());

            return passportConnectBuilder.updatePassportConnect(passportConnect);
        } catch (Exception e) {
            log.error("Failed to unbind wallet for passportId: {} and walletAddress: {}",
                    request.getPassportId(), request.getWalletAddress(), e);
            return false;
        }
    }

    @Override
    public List<PassportConnectDTO> getPassportConnect(String passportId) {
        List<PassportConnect> passportConnect = passportConnectBuilder.getByPassportId(passportId);
        passportConnect.removeIf(connect -> !WalletConstant.PassportConnectStatus.ACTIVE.getCode().equals(connect.getStatus()));
        return passportMapperStruct.toPassportConnectDTOList(passportConnect);
    }

    @Override
    public List<PassportConnectDTO> getPassportConnectByAddress(String address) {
        List<PassportConnect> byIdentifierList = passportConnectBuilder.getByIdentifier(address);
        byIdentifierList.removeIf(connect -> !WalletConstant.PassportConnectStatus.ACTIVE.getCode().equals(connect.getStatus()));
        return passportMapperStruct.toPassportConnectDTOList(byIdentifierList);
    }

    private void sendConnectEvent(PassportConnect passportConnect) {
        JSONObject body = new JSONObject();
        body.put("saasId",  customerProperties.getSaasId());
        EventDTO event = EventDTO
                .builder()
                .name("connect_" + passportConnect.getSubConnectProvider())
                .customerId(passportConnect.getPassportId())
                .globalUid(passportConnect.getIdentifier() + "_" + passportConnect.getPassportId() + "_" + passportConnect.getSubConnectProvider())
                .time(System.currentTimeMillis())
                .body(body)
                .build();
        log.info("passportConnect sendEvent, event:{}", event);
        eventClient.push(event);
    }

    private void sendWaitListEvent(String customerId, String scene, String series) {
        JSONObject body = new JSONObject();
        body.put("customerId", customerId);
        body.put("contentId", customerId + "_" + series + "_" + scene);
        body.put("series", series);
        body.put("scene", scene);
        EventDTO event = EventDTO.builder()
                .name("claimable_badge")
                .time(System.currentTimeMillis())
                .customerId(customerId)
                .globalUid(body.getString("contentId"))
                .source(eventClient.getDefaultSource())
                .body(body)
                .build();
        eventClient.asyncPush(event);
    }
}
