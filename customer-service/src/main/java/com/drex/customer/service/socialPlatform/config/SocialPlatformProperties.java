package com.drex.customer.service.socialPlatform.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/22 16:27
 */
@Configuration
@ConfigurationProperties(prefix = "three")
@Data
public class SocialPlatformProperties {

    @NestedConfigurationProperty
    private TwitterProperties twitter;
    @NestedConfigurationProperty
    private DiscordProperties discord;
    @NestedConfigurationProperty
    private GoogleProperties google;
    @NestedConfigurationProperty
    private TiktokProperties tiktok;
    @NestedConfigurationProperty
    private InstagramProperties instagram;
}
