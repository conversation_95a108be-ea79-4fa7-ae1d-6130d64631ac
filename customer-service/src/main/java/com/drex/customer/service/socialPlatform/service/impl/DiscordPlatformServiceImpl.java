package com.drex.customer.service.socialPlatform.service.impl;

import com.drex.customer.api.request.CustomerBindQuery;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.service.socialPlatform.AccessToken;
import com.drex.customer.service.socialPlatform.config.SocialPlatformProperties;
import com.drex.customer.api.constants.WalletConstant;
import com.drex.customer.service.socialPlatform.model.OpenAuthRequest;
import com.drex.customer.service.socialPlatform.model.SocialUserInfo;
import com.drex.customer.service.util.HttpPoolUtil;
import com.drex.customer.service.socialPlatform.service.SocialPlatformService;
import com.drex.model.CustomerException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.util.Base64;

/**
 * Discord平台服务实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DiscordPlatformServiceImpl implements SocialPlatformService {
    private static final String DC_AUTH_URL = "https://discord.com/api/oauth2/token";

    @Autowired
    private SocialPlatformProperties socialPlatformProperties;

    @Resource
    private CustomerBindService customerBindService;

    @Override
    public String getPlatformType() {
        return WalletConstant.PlatformEnum.Discord.name();
    }

    @Override
    public String generateAuthUrl() {
        return String.format("%s&client_id=%s&redirect_uri=%s",
                socialPlatformProperties.getDiscord().getAuthScore(),
                socialPlatformProperties.getDiscord().getClientId(),
                socialPlatformProperties.getDiscord().getPcRedirectUri());
    }

    @Override
    public OpenAuthRequest buildAuthRequest(String code) {
        return OpenAuthRequest.builder()
                .url(DC_AUTH_URL)
                .query(String.format("code=%s&grant_type=authorization_code&redirect_uri=%s",
                        code,
                        socialPlatformProperties.getDiscord().getPcRedirectUri()))
                .clientId(socialPlatformProperties.getDiscord().getClientId())
                .basic(Base64.getEncoder().encodeToString(
                        String.format("%s:%s",
                                socialPlatformProperties.getDiscord().getClientId(),
                                socialPlatformProperties.getDiscord().getClientSecret()).getBytes()))
                .build();
    }

    @Override
    public SocialUserInfo getCurrentUser(AccessToken accessToken) throws CustomerException {
        try {
            Request request = new Request.Builder()
                    .url("https://discord.com/api/v10/users/@me")
                    .addHeader("Authorization", "Bearer " + accessToken.getAccessToken())
                    .build();

            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            String responseBody = response.body().string();
            JSONObject jsonObject = new JSONObject(responseBody);
            log.info("[discord] currentUser: {}", jsonObject);

            if (!response.isSuccessful()) {
                log.error("Failed to get Discord user info: {}", responseBody);
                return null;
            }

            String id = jsonObject.getString("id");
            String username = jsonObject.getString("username");
            String avatarUrl = null;
            if (jsonObject.has("avatar") && !jsonObject.isNull("avatar")) {
                avatarUrl = String.format("https://cdn.discordapp.com/avatars/%s/%s.png",
                        id, jsonObject.getString("avatar"));
            }

            return SocialUserInfo.builder()
                    .userId(id)
                    .username(username)
                    .profileImageUrl(avatarUrl)
                    .createdAt(OffsetDateTime.now().toString())
                    .build();
        } catch (Exception e) {
            log.error("Error getting Discord user info", e);
            return null;
        }
    }

    @Override
    public CustomerBind validateUserBinding(SocialUserInfo userInfo) {
        if (userInfo == null) {
            return null;
        }

        // 构建查询参数，Discord平台使用默认的userId验证
        CustomerBindQuery query = CustomerBindQuery.builder()
                .socialPlatform(getPlatformType())
                .socialUserId(userInfo.getUserId())
                .socialUserName(userInfo.getUsername())
                .build();

        log.info("[DiscordPlatformService] Validating user binding with query: platform={}, userId={}, username={}",
                query.getSocialPlatform(), query.getSocialUserId(), query.getSocialUserName());

        return customerBindService.findBySocialInfo(query);
    }
}
