package com.drex.customer.service.reference.impl.model.anchor;

import co.evg.achievement.api.NftDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class ProfileModelConverter {

    public static List<NftDTO> ofNftDTO(List<BadgeAssetsResult.NftDTO> nfts) {
        if (CollectionUtils.isEmpty(nfts)) {
            return new ArrayList<>();
        }
        return nfts.stream().map(ProfileModelConverter::of).toList();
    }

    private static NftDTO of(BadgeAssetsResult.NftDTO nftDTO) {
        if (nftDTO == null) {
            return null;
        }
        NftDTO.Builder builder = NftDTO.newBuilder()
                .setAddress(nftDTO.getAddress())
                .setSeries(nftDTO.getSeries())
                .setSeriesName(nftDTO.getSeriesName())
                .setTier(nftDTO.getTier())
                .setLogo(nftDTO.getLogo())
                .setDesc(nftDTO.getDesc())
                .setMinCond(nftDTO.getMinCond())
                .setMaxCond(nftDTO.getMaxCond())
                .setStatus(nftDTO.getStatus())
                //.setRizz(nftDTO.getReward())
                .setClaimTime(nftDTO.getClaimTime())
                .setClaimableId(nftDTO.getClaimableId())
                .setType(nftDTO.getType())
                .setMintedCount(nftDTO.getMintedCount())
                .setTotalSupply(nftDTO.getTotalSupply())
                .setLevel(nftDTO.getLevel())
                .setName(nftDTO.getName())
                .setTokenId(Long.parseLong(nftDTO.getTokenId()));
        if (nftDTO.getSort() != null) {
            builder.setSort(nftDTO.getSort());
        }
        if (StringUtils.isNotBlank(nftDTO.getSeriesLogo())) {
            builder.setSeriesLogo(nftDTO.getSeriesLogo());
        }
        return builder.build();
    }
}
