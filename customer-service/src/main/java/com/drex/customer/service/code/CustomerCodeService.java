package com.drex.customer.service.code;

import com.alibaba.fastjson.JSONObject;
import com.drex.customer.api.request.CustomerCodeGenerateRequest;
import com.drex.customer.api.response.CustomerDTO;
import com.drex.customer.api.response.PassportDTO;
import com.drex.customer.dal.tablestore.model.Customer;
import com.drex.customer.dal.tablestore.model.CustomerCodeTransaction;
import com.drex.customer.dal.tablestore.model.CustomerCodes;
import com.drex.model.CustomerException;


public interface CustomerCodeService {

    /**
     * 生成邀请码
     * */
    String generateCode(CustomerCodeGenerateRequest request) throws CustomerException;

    /**
     * 检查某人，是否可以核销次类型code 是否满足核销场景
     * */
    CustomerCodes checkCode(CustomerCodes codesDO, String[] scene, String customerId) throws CustomerException;

    /**
     * 使用code，不校验类型, 但需要校验场景，防止在A场景使用B Code情况
     * 目前同一场景下没有等级区分,暂时把level删除掉
     * */
    CustomerCodeTransaction useCode(PassportDTO passportDTO, CustomerCodes codesDO, String[] scene) throws CustomerException ;

    /**
     * 需要扩展可重写该方法，attribute 放入扩展字段
     * @param codesDO
     * @param scene
     * @param attribute
     * @return
     * @throws CustomerException
     */
    default CustomerCodeTransaction useCode(PassportDTO passportDTO, CustomerCodes codesDO, String[] scene, JSONObject attribute) throws CustomerException {
        return useCode(passportDTO, codesDO, scene);
    }

}
