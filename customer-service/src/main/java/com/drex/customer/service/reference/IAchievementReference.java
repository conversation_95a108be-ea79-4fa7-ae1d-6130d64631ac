package com.drex.customer.service.reference;

import co.evg.achievement.api.BadgeAssetsStrategy;
import co.evg.achievement.api.NftDTO;
import co.evg.achievement.api.NftStatusEnum;
import com.drex.customer.service.reference.impl.model.anchor.BadgeSeriesResult;
import com.drex.model.CustomerException;

import java.util.List;
import java.util.Map;

public interface IAchievementReference {
    List<NftDTO> badgeAssets(String customerId, NftStatusEnum statusEnum, BadgeAssetsStrategy strategy);

    Map<String, List<NftDTO>> batchBadgeAssets(List<String> customerId, NftStatusEnum statusEnum, BadgeAssetsStrategy strategy);

    BadgeSeriesResult.BadgeSeriesDetail getNftSeries(String series) throws CustomerException;
}
