package com.drex.customer.service.business;

import com.drex.customer.api.response.CustomerDTO;
import com.drex.customer.api.request.AddWaitCreatorListRequest;
import com.drex.customer.api.request.AddWaitDeveloperListRequest;
import com.drex.customer.api.request.AddWaitListRequest;

public interface CustomerService {

    /**
     * 根据客户ID获取客户数据传输对象（DTO）。
     *
     * @param customerId 客户的唯一标识符。
     * @return 返回与指定客户ID对应的客户数据传输对象（DTO）。如果找不到对应的客户，则返回null。
     */
    CustomerDTO getById(String customerId);


    /**
     * 根据EOA地址获取客户数据传输对象（DTO）。
     *
     * @param eoaAddress 客户的EOA地址，用于唯一标识客户。
     * @return 返回与给定EOA地址对应的客户数据传输对象（DTO）。
     *         如果找不到对应的客户，则返回null。
     */
    CustomerDTO getByRegisterAddress(String eoaAddress);

    /**
     * 根据邀请码获取客户数据传输对象（DTO）。
     * @param inviteCode
     * @return
     */
    CustomerDTO getByInviteCode(String inviteCode);

    Boolean updateLevel(String customerId, String kycLevel);
    /**
     * 添加用户预订阅列表
     * @param addWaitListRequest
     * @return
     */
    boolean addWaitList(AddWaitListRequest addWaitListRequest);

    /**
     * 更新客户的connect_wallet字段
     * @param customerId 客户ID
     * @param walletAddress 钱包地址
     * @return 是否更新成功
     */
    boolean updateConnectWallet(String customerId, String walletAddress);

    /*
     * 添加创作者预订阅列表
     */
    boolean addWaitList(AddWaitCreatorListRequest addWaitCreatorListRequest);

    /**
     * 添加开发者预订阅列表
     * @param addWaitDeveloperListRequest
     * @return
     */
    boolean addWaitList(AddWaitDeveloperListRequest addWaitDeveloperListRequest);
}
