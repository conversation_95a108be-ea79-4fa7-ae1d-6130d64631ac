package com.drex.customer.service.socialPlatform;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/22 16:09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccessToken implements Serializable {

    protected String accessToken;
    protected String refreshToken;
    protected String userId;
    protected String currentUserName;
    protected String address;

}
