package com.drex.customer.service.config;

import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

/**
 * <AUTHOR>
 * @date 2025/4/28 17:05
 * @description:
 */
@Configuration
public class CustomerConfiguration {

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    @Bean("okHttpClient")
    public OkHttpClient okHttpClient(CustomerProperties customerProperties){
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        client.dispatcher().setMaxRequests(customerProperties.getHttpPoolSize());
        client.dispatcher().setMaxRequestsPerHost(customerProperties.getHttpPoolSize());
        return client;
    }

    /**
     * 社交同步处理线程池
     * 用于处理社交平台数据同步
     * 使用懒加载，只有在需要时才创建
     */
    @Bean("socialSyncExecutor")
    @Lazy
    public ScheduledExecutorService socialSyncExecutor(CustomerProperties customerProperties) {
        return Executors.newScheduledThreadPool(
                2 * Runtime.getRuntime().availableProcessors(),
                r -> {
                    Thread thread = new Thread(r, "social-sync-thread");
                    thread.setDaemon(true);
                    return thread;
                }
        );
    }
}
