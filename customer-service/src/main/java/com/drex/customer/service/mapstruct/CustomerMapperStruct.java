package com.drex.customer.service.mapstruct;

import com.drex.customer.api.response.CustomerDTO;
import com.drex.customer.dal.tablestore.model.Customer;
import com.drex.customer.api.request.AddWaitCreatorListRequest;
import com.drex.customer.api.request.AddWaitDeveloperListRequest;
import com.drex.customer.api.request.AddWaitListRequest;
import com.drex.customer.dal.tablestore.model.CustomerCreatorWaitList;
import com.drex.customer.dal.tablestore.model.CustomerDeveloperWaitList;
import com.drex.customer.dal.tablestore.model.CustomerWaitList;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CustomerMapperStruct {

    CustomerDTO toCustomerDTO(Customer customer);

    Customer toCustomer(CustomerDTO customer);

    @Mapping(target = "createTime", expression = "java(System.currentTimeMillis())")
    CustomerWaitList toCustomerWaitList(AddWaitListRequest addWaitListRequest);

    @Mapping(target = "createTime", expression = "java(System.currentTimeMillis())")
    CustomerCreatorWaitList toCustomerCreatorWaitList(AddWaitCreatorListRequest addWaitCreatorListRequest);

    @Mapping(target = "createTime", expression = "java(System.currentTimeMillis())")
    CustomerDeveloperWaitList toCustomerDeveloperWaitList(AddWaitDeveloperListRequest addWaitDeveloperListRequest);
}
