package com.drex.customer.service.mapstruct;

import com.drex.customer.api.response.ThirdAuthDTO;
import com.drex.customer.service.socialPlatform.model.Token;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2025/4/28 16:51
 * @description:
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AuthMapperStruct {

    ThirdAuthDTO toThirdAuthDTO(Token token);

}
