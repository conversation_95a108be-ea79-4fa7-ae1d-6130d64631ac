package com.drex.customer.service.business;

import com.drex.customer.api.constants.WalletConstant;
import com.drex.customer.api.request.AuthLoginRequest;
import com.drex.customer.api.request.BindWalletRequest;
import com.drex.customer.api.request.UnbindWalletRequest;
import com.drex.customer.api.response.PassportConnectDTO;
import com.drex.customer.api.response.PassportDTO;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

public interface PassportService {

    PassportDTO getPassportById(String passportId);

    PassportDTO getPassportByWalletAddress(String walletAddress, WalletConstant.PlatformEnum subConnectProvider);

    PassportDTO createPassport(AuthLoginRequest request);

    PassportDTO getByHandleName(String handleName);

    PassportDTO getByInviteCode(String inviteCode);

    Boolean updateHandleName(String passportId, String handleName);

    Pair<Boolean, String> updateOnChainInfo(String walletAddress, String passportChainId, String contractAddress);

    Boolean updateAvatar(String passportId, String avatar);

    Boolean bindWalletAddress(BindWalletRequest request);

    Boolean unbindWalletAddress(UnbindWalletRequest request);

    List<PassportConnectDTO> getPassportConnect(String passportId);

    List<PassportConnectDTO> getPassportConnectByAddress(String address);
}
