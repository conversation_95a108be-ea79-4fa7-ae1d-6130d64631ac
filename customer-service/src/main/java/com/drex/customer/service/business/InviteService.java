package com.drex.customer.service.business;

import com.drex.customer.api.request.CustomerReferralDTO;

public interface InviteService {

    String generateInviteCode();

    long countByReferrerId(String customerId);

    boolean bindInviteCode(CustomerReferralDTO customerReferralDTO);

    boolean updateReferrerStatusToValid(String customerId);

    /**
     * 根据customerId获取邀请人
     * @param customerId
     * @return
     */
    String getReferrerByCustomerId(String customerId);
}
