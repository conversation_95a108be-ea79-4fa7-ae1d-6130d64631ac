package com.drex.customer.service.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.drex.customer.service.business.PassportService;
import com.drex.customer.service.cache.PassportCacheService;
import com.kikitrade.framework.ons.OnsMessageListener;
import com.kikitrade.framework.ons.OnsProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Objects;


@Component
@Slf4j
public class EndpointEventListener implements OnsMessageListener {

    @Resource
    private OnsProperties onsProperties;
    @Resource
    @Lazy
    private PassportService passportService;
    @Resource
    @Lazy
    private PassportCacheService passportCacheService;

    private static final String TOPIC = "T_ENDPOINT_EVENT_DREX";

    @Override
    public String topic() {
        return TOPIC + onsProperties.getEnv();
    }

    @Override
    public Action doConsume(Message message, ConsumeContext context) {
        log.info("EndpointEventListener: {},{},{}", message.getMsgID(), message.getTopic(), new String(message.getBody()));
        // {"eventType":"PassportCreated","initialWallet":"******************************************","log":{"address":"******************************************","blockHash":"0xae77427f4de00becf2c7452f23b940feee59a37933c21cab5b06cd9d354807a0","blockNumber":"3804","data":"0x0000000000000000000000001be203b41381f520ba96a27f795be9c9d66ca3d4","logIndex":"2","removed":false,"topics":["0x1931bc99fb76e73709b851f1a5c3cab95d8a0db0b6591defbfb6ce28b0a0a348","0x0000000000000000000000000000000000000000000000000000000000000002","0x0000000000000000000000001874c064452b0e1061c979819c075f876673568f"],"transactionHash":"0x1b91d1edaa1e1faf2b81506d5387493dcbb4541d4dae9e68d9f2524d02ef325a","transactionIndex":"1"},"passportContract":"******************************************","passportId":2}
        if (StringUtils.isNotBlank(new String(message.getBody()))) {
            JSONObject body = JSON.parseObject(new String(message.getBody()));
            if (Objects.nonNull(body) && body.containsKey("eventType")) {
                String eventType = body.getString("eventType");
                log.info("handle receive message eventType: {}", eventType);
                switch (eventType) {
                    case "PassportCreated":
                        Pair<Boolean, String> updateResult = passportService.updateOnChainInfo(body.getString("initialWallet"), body.getString("passportId"), body.getString("passportContract"));
                        boolean updated = updateResult.getLeft();
                        if (updated) {
                            passportCacheService.delete(updateResult.getRight());
                            log.info("EndpointEventListener success: {},{},{}", message.getMsgID(), message.getTopic(), new String(message.getBody()));
                            return Action.CommitMessage;
                        }
                        break;
                    case "WalletBound":
                    case "WalletUnbound":
                    default:
                        break;
                }
            }
        }
        return Action.ReconsumeLater;
    }
}
