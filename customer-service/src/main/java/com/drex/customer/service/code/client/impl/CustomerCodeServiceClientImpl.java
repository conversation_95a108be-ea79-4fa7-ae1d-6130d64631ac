package com.drex.customer.service.code.client.impl;

import com.alibaba.fastjson.JSONObject;
import com.drex.customer.api.ErrorCode;
import com.drex.customer.api.request.CustomerCodeGenerateRequest;
import com.drex.customer.api.response.CustomerCodesDTO;
import com.drex.customer.api.response.PassportDTO;
import com.drex.customer.dal.tablestore.builder.CustomerCodeTransactionBuilder;
import com.drex.customer.dal.tablestore.builder.CustomerCodesBuilder;
import com.drex.customer.dal.tablestore.model.CustomerCodeTransaction;
import com.drex.customer.dal.tablestore.model.CustomerCodes;
import com.drex.customer.service.code.CustomerCodeService;
import com.drex.customer.service.code.client.CustomerCodeServiceClient;
import com.drex.customer.service.mapstruct.CustomerCodesMapperStruct;
import com.drex.model.CacheKey;
import com.drex.model.CustomerException;
import com.kikitrade.framework.redis.lock.RedisDistributedLock;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;

/**
 * @ClassName CustomerCodeServiceClient
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/20 19:14
 **/
@Service
@Slf4j
public class CustomerCodeServiceClientImpl implements CustomerCodeServiceClient {

    @Resource
    CustomerCodesBuilder customerCodesBuilder;

    @Resource
    CustomerCodeTransactionBuilder customerCodeTransactionBuilder;

    @Autowired
    Map<String, CustomerCodeService> customerCodeServiceMap;

    @Autowired
    protected RedisDistributedLock redisDistributedLock;

    @Resource
    private CustomerCodesMapperStruct customerCodesMapperStruct;

    CustomerCodeService getService(String type) {
        return customerCodeServiceMap.get(type);
    };

    @Override
    public String generateCode(CustomerCodeGenerateRequest request) throws CustomerException {
        return getService(request.getType().getName()).generateCode(request);
    }

    @Override
    public Boolean checkCode(String code, String[] scene, String customerId) throws CustomerException {
        CustomerCodes customerCodesDO = customerCodesBuilder.getByCode(code);
        if (Objects.isNull(customerCodesDO)) {
            log.info("code not exist: {}", code);
            throw new CustomerException(ErrorCode.CODE_NOT_EXIST);
        }
        return getService(customerCodesDO.getType()).checkCode(customerCodesDO, scene, customerId) != null;
    }

    @Override
    public CustomerCodesDTO useCode(PassportDTO passportDTO, String code, String[] scene, JSONObject attribute) throws CustomerException {
        log.info("use code start: customerId {} code {} ", passportDTO.getPassportId(), code);
        CustomerCodes customerCodesDO = customerCodesBuilder.getByCode(code);
        if (Objects.isNull(customerCodesDO)) {
            log.info("code not exist: {}", code);
            throw new CustomerException(ErrorCode.CODE_NOT_EXIST);
        }
        CustomerCodeTransaction codesDO = null;
        String key = CacheKey.INVITE_RELATION_LOCK.getKey() + customerCodesDO.getCustomerId();
        boolean lock = redisDistributedLock.tryLock(key, Duration.ofSeconds(5));
        if(lock){
            try {
                long count = customerCodeTransactionBuilder.getUsedCount(code);
                // 核销验证码
                codesDO = getService(customerCodesDO.getType()).useCode(passportDTO, customerCodesDO, scene, attribute);
            } catch (CustomerException customerException) {
                log.error("useCode error, customerDO: {}, code: {}, scene: {}", passportDTO, code, scene);
                throw customerException;
            } catch (Exception ex) {
                log.error("useCode error, customerDO: {}, code: {}, scene: {}", passportDTO, code, scene, ex);
            } finally {
                redisDistributedLock.unlock(key);
            }
        }

        return customerCodesMapperStruct.toCustomerCodesDTO(codesDO);
    }
}
