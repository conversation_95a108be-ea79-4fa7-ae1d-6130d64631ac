package com.drex.customer.service.mapstruct;

import com.drex.customer.api.response.PassportConnectDTO;
import com.drex.customer.api.response.PassportDTO;
import com.drex.customer.dal.tablestore.model.Passport;
import com.drex.customer.dal.tablestore.model.PassportConnect;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PassportMapperStruct {

    @Mapping(target = "isNewPassport", expression = "java(passport.getHandleName() == null ? Boolean.TRUE : Boolean.FALSE)")
    PassportDTO toPassportDTO(Passport passport);

    Passport toPassport(PassportDTO passportDTO);

    List<PassportConnectDTO> toPassportConnectDTOList(List<PassportConnect> passportConnect);
}
