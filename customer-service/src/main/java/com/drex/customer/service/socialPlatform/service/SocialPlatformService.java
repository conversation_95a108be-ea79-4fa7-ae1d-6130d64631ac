package com.drex.customer.service.socialPlatform.service;

import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.service.socialPlatform.AccessToken;
import com.drex.customer.service.socialPlatform.model.OpenAuthRequest;
import com.drex.customer.service.socialPlatform.model.SocialUserInfo;
import com.drex.model.CustomerException;

/**
 * 社交平台服务接口
 * 统一不同社交平台的操作
 * <AUTHOR>
 */
public interface SocialPlatformService {

    /**
     * 获取平台类型
     * @return 平台类型
     */
    String getPlatformType();

    /**
     * 生成授权URL
     * @return 授权URL
     */
    String generateAuthUrl();

    /**
     * 构建授权请求
     * @param code 授权码
     * @return 授权请求对象
     */
    OpenAuthRequest buildAuthRequest(String code);

    /**
     * 获取当前用户信息
     * @param accessToken 访问令牌
     * @return 用户信息
     */
    SocialUserInfo getCurrentUser(AccessToken accessToken) throws CustomerException;

    /**
     * 验证用户是否已被其他用户绑定
     * 不同平台使用不同的验证字段：
     * - Google: 优先使用email字段验证
     * - X(Twitter): 优先使用username字段验证
     * - 其他平台: 使用userId字段验证
     *
     * @param userInfo 社交平台用户信息
     * @return 如果已被绑定则返回绑定记录，否则返回null
     */
    CustomerBind validateUserBinding(SocialUserInfo userInfo);
}
