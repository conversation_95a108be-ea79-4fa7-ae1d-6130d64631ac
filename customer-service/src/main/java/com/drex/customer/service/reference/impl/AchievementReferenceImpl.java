package com.drex.customer.service.reference.impl;

import cn.hutool.http.ContentType;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import co.evg.achievement.api.BadgeAssetsStrategy;
import co.evg.achievement.api.NftDTO;
import co.evg.achievement.api.NftStatusEnum;
import com.alibaba.fastjson.JSONObject;
import com.drex.customer.api.ErrorCode;
import com.drex.customer.service.config.CustomerProperties;
import com.drex.customer.service.reference.IAchievementReference;
import com.drex.customer.service.reference.impl.model.anchor.AccessTokenResult;
import com.drex.customer.service.reference.impl.model.anchor.AnchorConstants;
import com.drex.customer.service.reference.impl.model.anchor.BadgeAssetsResult;
import com.drex.customer.service.reference.impl.model.anchor.BadgeSeriesResult;
import com.drex.customer.service.reference.impl.model.anchor.ProfileModelConverter;
import com.drex.model.CustomerException;
import com.kikitrade.framework.ons.OnsProducer;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * achievement 系统相关API
 */
@Service
@Slf4j
public class AchievementReferenceImpl implements IAchievementReference {

    private static final int TIMEOUT_MILLISECONDS = 5000;
    private static final int ACCESS_TOKEN_EXPIRE_BUFFER = 60 * 10;

    @Resource
    private CustomerProperties customerProperties;
    @Resource
    @Lazy
    private OnsProducer onsProducer;
    @Resource
    RedisTemplate<String, String> redisTemplate;

    private String getAccessToken() throws CustomerException {
        String anchorClientId = customerProperties.getAnchorClientId();
        String key = String.format(AnchorConstants.CACHE_KEY_ACCESS_TOKEN, anchorClientId);
        String acJson = redisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(acJson)) {
            log.info("achievementReferenceImpl getAccessToken from redis, key:{}, value: {}", key, acJson);
            AccessTokenResult accessTokenResult = JSONObject.parseObject(acJson, AccessTokenResult.class);
            return accessTokenResult.getAccessToken();
        }

        HttpRequest request = HttpUtil.createPost(customerProperties.getAnchorHost() + AnchorConstants.URL_ACCESS_TOKEN)
                .header(Header.CONTENT_TYPE.getValue(), ContentType.FORM_URLENCODED.getValue())
                .form(AnchorConstants.PARAM_GRANT_TYPE, AnchorConstants.VALUE_GRANT_TYPE)
                .form(AnchorConstants.PARAM_CLIENT_ID, anchorClientId)
                .form(AnchorConstants.PARAM_CLIENT_SECRET, customerProperties.getAnchorClientSecret())
                .timeout(TIMEOUT_MILLISECONDS);

        HttpResponse response = request.execute();
        if (response.getStatus() == HttpStatus.HTTP_OK) {
            AccessTokenResult result = JSONObject.parseObject(response.body(), AccessTokenResult.class);
            if (result == null || StringUtils.isBlank(result.getAccessToken())) {
                log.warn("achievementReferenceImpl getAccessToken fail, clientId: {}, body:{}", anchorClientId, response.body());
                throw new CustomerException(ErrorCode.GET_ANCHOR_ACCESS_TOKEN_FAIL);
            }
            int timeout = result.getExpiresIn() - ACCESS_TOKEN_EXPIRE_BUFFER;
            redisTemplate.opsForValue().set(key, JSONObject.toJSONString(result), timeout, java.util.concurrent.TimeUnit.SECONDS);
            log.info("achievementReferenceImpl getAccessToken from anchor, key:{}, value: {}", key, JSONObject.toJSONString(result));
            return result.getAccessToken();
        } else {
            log.error("achievementReferenceImpl getAccessToken error, clientId: {}, response:{}", anchorClientId, response);
            return null;
        }
    }

    @Override
    public List<NftDTO> badgeAssets(String customerId, NftStatusEnum statusEnum, BadgeAssetsStrategy strategy) {
        Map<String, List<NftDTO>> batchBadgeAssets = this.batchBadgeAssets(List.of(customerId), statusEnum, strategy);
        return batchBadgeAssets.getOrDefault(customerId, new ArrayList<>());
    }

    @Override
    public Map<String, List<NftDTO>> batchBadgeAssets(List<String> customerIds, NftStatusEnum statusEnum, BadgeAssetsStrategy strategy) {
        int limit = 150;
        Map<String, List<NftDTO>> badgeAssetsMap = new HashMap<>();
        try {
            JSONObject body = new JSONObject();
            body.put("customerIds", customerIds);
            body.put("status", StringUtils.lowerCase(statusEnum.name()));
            body.put("strategy", strategy.name());
            body.put("limit", limit);
            String url = customerProperties.getAnchorHost() + AnchorConstants.URL_ASSETS_BADGE;
            HttpRequest request = HttpUtil.createPost(url)
                    .addHeaders(buildHeaders())
                    .body(body.toJSONString())
                    .timeout(TIMEOUT_MILLISECONDS);
            HttpResponse response = request.execute();
            if (response.getStatus() == HttpStatus.HTTP_OK) {
                BadgeAssetsResult result = JSONObject.parseObject(response.body(), BadgeAssetsResult.class);
                if (result == null || !result.isSuccess()) {
                    log.warn("achievementReferenceImpl batchBadgeAssets fail, customerIds: {}, statusEnum: {}, strategy: {}, body:{}", customerIds, statusEnum, strategy, response.body());
                    return badgeAssetsMap;
                }
                log.info("achievementReferenceImpl batchBadgeAssets from anchor, customerIds:{}, statusEnum: {}, strategy: {}, result: {}", customerIds, statusEnum, strategy, result);
                BadgeAssetsResult.BadgeAssetsDTO badgeAssetsDTO = result.getObj();
                if (badgeAssetsDTO == null) {
                    return badgeAssetsMap;
                }
                List<BadgeAssetsResult.BadgeAssets> rows = badgeAssetsDTO.getRows();
                return rows.stream().collect(Collectors.toMap(BadgeAssetsResult.BadgeAssets::getCustomerId, v -> ProfileModelConverter.ofNftDTO(v.getNfts())));
            } else {
                log.error("achievementReferenceImpl batchBadgeAssets error, customerIds: {}, statusEnum: {}, strategy: {}, response:{}", customerIds, statusEnum, strategy, response);
                return badgeAssetsMap;
            }
        } catch (CustomerException e) {
            log.error("achievementReferenceImpl batchBadgeAssets error, customerIds: {}, statusEnum: {}, strategy: {}", customerIds, statusEnum, strategy, e);
        }
        return badgeAssetsMap;
    }

    @Override
    public BadgeSeriesResult.BadgeSeriesDetail getNftSeries(String series) throws CustomerException {
        String url = customerProperties.getAnchorHost() + String.format(AnchorConstants.URL_SERIES_DETAIL, series);
        HttpRequest request = HttpUtil.createGet(url)
                .addHeaders(buildHeaders())
                .timeout(TIMEOUT_MILLISECONDS);
        HttpResponse response = request.execute();
        if (response.getStatus() == HttpStatus.HTTP_OK) {
            BadgeSeriesResult result = JSONObject.parseObject(response.body(), BadgeSeriesResult.class);
            if (result == null || !result.isSuccess()) {
                log.warn("achievementReferenceImpl getNftSeries fail, series: {}, body:{}", series, response.body());
                return null;
            }
            log.info("achievementReferenceImpl getNftSeries from anchor, series:{}, result: {}", series, result);
            return result.getObj();
        } else {
            log.error("achievementReferenceImpl getNftSeries error, series: {}, response:{}", series, response);
            return null;
        }
    }

    private Map<String, String> buildHeaders() throws CustomerException {
        Map<String, String> headers = new HashMap<>();
        String accessToken = AnchorConstants.AUTHORIZATION_PREFIX + getAccessToken();
        if (StringUtils.isBlank(accessToken)) {
            log.error("get anchor access token fail");
            throw new CustomerException(ErrorCode.GET_ANCHOR_ACCESS_TOKEN_FAIL);
        }
        headers.put(AnchorConstants.HEADER_AUTHORIZATION, accessToken);
        headers.put(AnchorConstants.HEADER_CLIENT_ID, customerProperties.getAnchorProjectId());
        headers.put(AnchorConstants.HEADER_CHAIN_ID, customerProperties.getAnchorChainId());
        log.info("send anchor api headers:{}", JSONObject.toJSONString(headers));
        return headers;
    }

}
