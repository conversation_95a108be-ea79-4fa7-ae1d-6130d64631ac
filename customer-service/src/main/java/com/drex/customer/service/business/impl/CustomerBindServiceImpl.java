package com.drex.customer.service.business.impl;

import co.evg.scaffold.event.client.EventClient;
import co.evg.scaffold.event.client.EventDTO;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.drex.customer.api.constants.CustomerConstants;
import com.drex.customer.api.constants.WalletConstant;
import com.drex.customer.api.request.CustomerBindQuery;
import com.drex.customer.dal.tablestore.builder.CustomerBindBuilder;
import com.drex.customer.dal.tablestore.builder.CustomerWaitListBuilder;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.dal.tablestore.model.CustomerWaitList;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.service.business.InviteService;
import com.drex.customer.service.config.CustomerProperties;
import com.drex.customer.service.socialPlatform.service.SocialPlatformServiceFactory;
import com.drex.model.CacheKey;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/28 10:15
 * @description:
 */
@Slf4j
@Service
public class CustomerBindServiceImpl implements CustomerBindService {
    @Resource
    private CustomerBindBuilder customerBindBuilder;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private CustomerWaitListBuilder customerWaitListBuilder;
    @Resource
    private InviteService inviteService;
    @Resource
    private CustomerProperties customerProperties;
    @Resource
    private EventClient eventClient;


    private final static String eventCodePrefix = "connect_";

    @Override
    public boolean insert(CustomerBind customerBind) {
        String cacheKey = String.format("%s:%s", CacheKey.CUSTOMER_BIND.getKey(), customerBind.getCustomerId());
        redisTemplate.delete(cacheKey);
        if (customerBindBuilder.insert(customerBind)) {
            //触发邀请逻辑
            List<CustomerBind> customerBinds = customerBindBuilder.findByCustomerId(customerBind.getCustomerId());
            if (customerBinds == null) {
                customerBinds = List.of();
            }
            //只有google和email校验socialEmail,其他平台校验socialUserId不为空
            customerBinds = customerBinds.stream().filter(bind -> {
                String platform = bind.getSocialPlatform();
                if (platform == null) {
                    return false;
                }
                if (WalletConstant.PlatformEnum.Email.name().equalsIgnoreCase(platform) ||
                        WalletConstant.PlatformEnum.Google.name().equalsIgnoreCase(platform)) {
                    return StringUtils.isNotBlank(bind.getSocialEmail());
                }
                return StringUtils.isNotBlank(bind.getSocialUserId());
            }).toList();
            if(customerBinds.size() >= 2){
                inviteService.updateReferrerStatusToValid(customerBind.getCustomerId());
            }
            checkWaitListBadges(customerBind);
            checkSocialMediaBadges(customerBind);
            return true;
        } else {
            return false;
        }
    }

    public void checkWaitListBadges(CustomerBind customerBind){
        try {
            CustomerWaitList waitList =
                    customerWaitListBuilder.getWaitListByAddressOrEmail(customerBind.getSocialEmail() ,null, 0l, customerProperties.getCustomerWaitListEffectiveTime());
            if (waitList != null) {
                log.info("find wait list customer {}, social email {}", customerBind.getCustomerId(), customerBind.getSocialEmail());
                sendWaitListEvent(customerBind.getCustomerId(), CustomerConstants.SCENE_WEBSITE_WAITLIST, CustomerConstants.SERIES_TREX_JOURNEY);
            }
        }catch (Exception e){
            log.error("checkBadges exception", e);
        }
    }

    public void checkSocialMediaBadges(CustomerBind customerBind){
        try {
            // 所有需要手动绑定的社媒类型
            List<String> socialPlatforms = SocialPlatformServiceFactory.getNeedManualBindPlatforms();
            // 用户当前已绑定的社媒类型
            List<CustomerBind> byCustomerId = customerBindBuilder.findByCustomerId(customerBind.getCustomerId());
            Set<String> boundPlatforms = byCustomerId.stream().map(CustomerBind::getSocialPlatform).collect(Collectors.toSet());
            // 检查是否所有需要手动绑定的平台都已绑定
            boolean allBound = boundPlatforms.containsAll(socialPlatforms);
            if (allBound) {
                log.info("social media customer {} begin send badge", customerBind.getCustomerId());
                sendWaitListEvent(customerBind.getCustomerId(), CustomerConstants.SCENE_SOCIAL_MEDIA, CustomerConstants.SERIES_CORE_IDENTITY);
            }
        }catch (Exception e){
            log.error("checkBadges exception", e);
        }
    }

    private void sendWaitListEvent(String customerId, String scene, String series) {
        JSONObject body = new JSONObject();
        body.put("customerId", customerId);
        body.put("contentId", customerId + "_" + series + "_" + scene);
        body.put("series", series);
        body.put("scene", scene);
        EventDTO event = EventDTO.builder()
                .name("claimable_badge")
                .time(System.currentTimeMillis())
                .customerId(customerId)
                .globalUid(body.getString("contentId"))
                .source(eventClient.getDefaultSource())
                .body(body)
                .build();
        eventClient.asyncPush(event);
    }

    @Override
    public boolean delete(CustomerBind customerBind) {
        String cacheKey = String.format("%s:%s", CacheKey.CUSTOMER_BIND.getKey(), customerBind.getCustomerId());
        redisTemplate.delete(cacheKey);
        return customerBindBuilder.delete(customerBind);
    }

    @Override
    public CustomerBind findByCustomerId(String customerId, String socialPlatform) {
        return customerBindBuilder.findByCustomerId(customerId, socialPlatform);
    }

    @Override
    public List<CustomerBind> findByCustomerId(String customerId, List<String> socialPlatforms) {
        String cacheKey = String.format("%s:%s", CacheKey.CUSTOMER_BIND.getKey(), customerId);
        String cache = redisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isNotBlank(cache)) {
            return JSON.parseArray(cache, CustomerBind.class).stream().filter(customerBind -> socialPlatforms.contains(customerBind.getSocialPlatform())).toList();
        }
        List<CustomerBind> customerBinds = customerBindBuilder.findByCustomerId(customerId, socialPlatforms);
        if (!customerBinds.isEmpty()) {
            redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(customerBinds), 3, TimeUnit.DAYS);
        }
        return customerBinds;
    }


    @Override
    public CustomerBind findBySocialInfo(CustomerBindQuery query) {
        CustomerBindQuery customerBindQuery = new CustomerBindQuery();
        customerBindQuery.setSocialPlatform(query.getSocialPlatform());

        WalletConstant.PlatformEnum platformEnum = WalletConstant.PlatformEnum.getEnumByName(query.getSocialPlatform());
        switch (platformEnum) {
            case Email,Google:
                customerBindQuery.setSocialEmail(query.getSocialEmail());
                break;
            case X,Discord,Telegram:
                customerBindQuery.setSocialUserId(query.getSocialUserId());
                break;
            case Facebook:
                customerBindQuery.setSocialUserName(query.getSocialUserName());
                break;
            default:
                customerBindQuery = query;
                break;
        }
        return customerBindBuilder.findBySocialInfo(customerBindQuery);
    }

    @Override
    public boolean reservePrivacyAuth(String customerId, String socialPlatform, String privacyAuth) {
        CustomerBind customerBind = customerBindBuilder.findByCustomerId(customerId, socialPlatform);
        if (customerBind != null) {
            if (StringUtils.isNotBlank(privacyAuth)) {
                customerBind.setPrivacyAuth(Boolean.parseBoolean(privacyAuth));
            } else {
                customerBind.setPrivacyAuth(!customerBind.getPrivacyAuth());
            }
        } else {
            customerBind = new CustomerBind();
            customerBind.setCustomerId(customerId);
            customerBind.setSocialPlatform(socialPlatform);
            customerBind.setPrivacyAuth(true);
            customerBind.setCreated(System.currentTimeMillis());
        }
        return insert(customerBind);
    }
}
