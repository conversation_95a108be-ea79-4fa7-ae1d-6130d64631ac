package com.drex.customer.service.business.impl;

import cn.hutool.core.util.RandomUtil;
import co.evg.scaffold.event.client.EventClient;
import co.evg.scaffold.event.client.EventDTO;
import com.alibaba.fastjson.JSONObject;
import com.drex.customer.api.constants.CustomerConstants;
import com.drex.customer.api.request.CustomerReferralDTO;
import com.drex.customer.api.response.PassportDTO;
import com.drex.customer.dal.tablestore.builder.CustomerReferralBuilder;
import com.drex.customer.dal.tablestore.model.CustomerReferral;
import com.drex.customer.service.business.PassportService;
import com.drex.customer.service.business.InviteService;
import com.drex.customer.service.config.CustomerProperties;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.framework.ons.OnsProperties;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

@Service
public class InviteServiceImpl implements InviteService {

    private static final String BASE_CHAT = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

    @Resource
    private CustomerReferralBuilder customerReferralBuilder;
    @Resource
    private OnsProducer onsProducer;
    @Resource
    private CustomerProperties customerProperties;
    @Resource
    private OnsProperties onsProperties;
    @Resource
    @Lazy
    private PassportService passportService;
    @Resource
    private EventClient eventClient;

    @Override
    public String generateInviteCode() {
        do{
            String code = RandomUtil.randomString(BASE_CHAT, 8);
            if(passportService.getByInviteCode(code) == null){
                return code;
            }
        }while (true);
    }

    @Override
    public long countByReferrerId(String customerId) {
        return customerReferralBuilder.countByReferrerId(customerId);
    }

    @Override
    public boolean bindInviteCode(CustomerReferralDTO customerReferralDTO) {
        PassportDTO customerDTO = passportService.getByInviteCode(customerReferralDTO.getInviteCode());
        if(customerDTO == null){
            return false;
        }
        customerReferralDTO.setReferrerId(customerDTO.getPassportId());

        CustomerReferral customerReferral = new CustomerReferral();
        customerReferral.setReferrerId(customerDTO.getPassportId());
        customerReferral.setCustomerId(customerReferralDTO.getCustomerId());
        customerReferral.setBusinessType("bindInviteCode");
        customerReferral.setCreated(System.currentTimeMillis());
        customerReferral.setStatus(CustomerConstants.RefferalStatus.INVALID.getKey());
        return customerReferralBuilder.insert(customerReferral);
    }

    public boolean updateReferrerStatusToValid(String customerId) {
        boolean success = customerReferralBuilder.updateReferrerStatus(customerId, CustomerConstants.RefferalStatus.VALID.getKey(), "bindInviteCode");
        if(success){
            CustomerReferral referral = customerReferralBuilder.getByCustomerId(customerId, "bindInviteCode");
            JSONObject body = new JSONObject();
            body.put("saasId",  customerProperties.getSaasId());
            body.put("inviteeId", referral.getCustomerId());
            //发送消息到event
            EventDTO event = EventDTO.builder()
                    .name("invited_register")
                    .time(System.currentTimeMillis())
                    .customerId(referral.getReferrerId())
                    .globalUid(customerId + "_" + referral.getReferrerId())
                    .body(body)
                    .build();
            eventClient.push(event);
        }
        return success;
    }

    /**
     * 查询我的邀请人
     * @param customerId
     * @return
     */
    @Override
    public String getReferrerByCustomerId(String customerId) {
        CustomerReferral bindInviteCode = customerReferralBuilder.getByCustomerId(customerId, "bindInviteCode");
        return bindInviteCode != null ? bindInviteCode.getReferrerId() : null;
    }
}
