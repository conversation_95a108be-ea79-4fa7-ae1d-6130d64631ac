package com.drex.customer.service.code;

import com.drex.customer.api.ErrorCode;
import com.drex.customer.api.constants.CustomerConstants;
import com.drex.customer.dal.tablestore.builder.CustomerCodeTransactionBuilder;
import com.drex.customer.dal.tablestore.builder.CustomerCodesBuilder;
import com.drex.customer.dal.tablestore.model.CustomerCodeTransaction;
import com.drex.customer.dal.tablestore.model.CustomerCodes;
import com.drex.model.CustomerException;
import com.drex.model.CacheKey;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * @ClassName AbstractCustomerCodeService
 * @Description 公共方法抽离
 * <AUTHOR>
 * @Date 2024/5/20 19:13
 **/
@Service
@Slf4j
public abstract class AbstractCustomerCodeService implements CustomerCodeService {

    @Resource
    CustomerCodesBuilder customerCodesBuilderImp;
    @Resource
    CustomerCodeTransactionBuilder customerCodeTransactionBuilder;

    @Resource
    RedisTemplate redisTemplate;


    // 默认检查，检查code是否存在，状态是否正确，场景是否正确
    public CustomerCodes defaultCheck(CustomerCodes codesDO, String[] scene, String customerId) throws CustomerException {
        CustomerCodeTransaction customerCodeTransactionDO = customerCodeTransactionBuilder.getCodesUser(codesDO.getCode());
        if (Objects.isNull(codesDO)) {
            throw new CustomerException(ErrorCode.CODE_NOT_EXIST);
        }
        if (!codesDO.getStatus().equals(CustomerConstants.Status.EFFECTIVE.getName())) {
            throw new CustomerException(ErrorCode.CODE_STATUS_NOT_EFFECTIVE);
        }
        // 多场景检查
        if (ArrayUtils.isEmpty(scene) || !ArrayUtils.contains(scene, codesDO.getScene())) {
            throw new CustomerException(ErrorCode.CODE_SCENE_ERROR);
        }
        if (codesDO.getExpirationTime() != null && codesDO.getExpirationTime().before(new Date())) {
            throw new CustomerException(ErrorCode.CODE_IS_EXPIRED);
        }
        if (!codesDO.getCanReuse()) {
            if (Objects.nonNull(customerCodeTransactionDO)) {
                if(customerId == null || !customerId.equals(customerCodeTransactionDO.getUserId())){
                    // It can be reused, but the same person cannot use it multiple times within 2 seconds
                    throw new CustomerException(ErrorCode.CODE_ALREADY_USED_BY_OTHER);
                } else {
                    // It can be reused, and the same person cannot use it multiple times within 2 seconds
                    throw new CustomerException(ErrorCode.CODE_ALREADY_USED_BY_YOU);
                }
            }
        } else if (Objects.nonNull(customerCodeTransactionDO) && Double.valueOf(customerCodeTransactionDO.getUseTime().getTime() - System.currentTimeMillis())/1000 > 2){
            // It can be reused in time, and the same person cannot use it multiple times within 2 seconds
            throw new CustomerException(ErrorCode.CODE_USED_TOO_FREQUENTLY);
        }
        return codesDO;
    }

    public String defaultGenerateCode() {
        String code = generateShortUuid(6);
        boolean has = hasCode(code);
        while (has) {
            code = generateShortUuid(6);
            has = hasCode(code);
        }
        return code;
    }

    /**
     * 批量获取code
     * */
    public List<String> batchDefaultGenerateCode(int count) {
        List<String> codes = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            codes.add(defaultGenerateCode());
        }
        return codes;
    }
    public boolean hasCode(String code) {
        boolean has = customerCodesBuilderImp.getByCode(code) != null;
        if (!has) {
            //如果不存在 add
            Boolean add = redisTemplate.opsForZSet().add(CacheKey.RECENT_INVITE_CODE.getKey(), code, System.currentTimeMillis());
            //如果add不成功表示有
            has = Boolean.FALSE.equals(add);
        }
        //清除缓存中的记录 防止数量过大
        clearRedis();
        return has;
    }
    public String generateShortUuid(int len) {
        StringBuilder shortBuffer = new StringBuilder();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        for (int i = 0; i < len; i++) {
            String str = uuid.substring(i * 4, i * 4 + 4);
            int x = Integer.parseInt(str, 16);
            shortBuffer.append(chars[x % 0x3E]);
        }
        return shortBuffer.toString().toUpperCase();
    }

    public static String[] chars = new String[]{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};

    public void clearRedis() {
        //清除缓存中的记录 防止数量过大
        CompletableFuture.runAsync(() -> {
            Long size = redisTemplate.opsForZSet().size(CacheKey.RECENT_INVITE_CODE.getKey());
            if (size != null && size % 200 == 0) {
                //只保留最近五分钟
                LocalDateTime localDateTime = LocalDateTime.now().minusMinutes(5);
                ZoneId zone = ZoneId.systemDefault();
                Instant instant = localDateTime.atZone(zone).toInstant();
                redisTemplate.opsForZSet().removeRangeByScore(CacheKey.RECENT_INVITE_CODE.getKey(), 0, instant.toEpochMilli());
            }
        });
    }
}
