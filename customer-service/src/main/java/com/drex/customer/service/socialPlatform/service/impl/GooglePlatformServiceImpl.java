package com.drex.customer.service.socialPlatform.service.impl;

import com.drex.customer.api.request.CustomerBindQuery;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.service.socialPlatform.AccessToken;
import com.drex.customer.service.socialPlatform.config.SocialPlatformProperties;
import com.drex.customer.api.constants.WalletConstant;
import com.drex.customer.service.socialPlatform.model.OpenAuthRequest;
import com.drex.customer.service.socialPlatform.model.SocialUserInfo;
import com.drex.customer.service.util.HttpPoolUtil;
import com.drex.customer.service.socialPlatform.service.SocialPlatformService;
import com.drex.model.CustomerException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Base64;

/**
 * Google平台服务实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class GooglePlatformServiceImpl implements SocialPlatformService {

    private static final String GOOGLE_AUTH_URL = "https://oauth2.googleapis.com/token";

    @Autowired
    private SocialPlatformProperties socialPlatformProperties;

    @Resource
    private CustomerBindService customerBindService;

    @Override
    public String getPlatformType() {
        return WalletConstant.PlatformEnum.Google.name();
    }

    @Override
    public String generateAuthUrl() {
        return String.format("%s&client_id=%s&redirect_uri=%s",
                socialPlatformProperties.getGoogle().getAuthScore(),
                socialPlatformProperties.getGoogle().getClientId(),
                socialPlatformProperties.getGoogle().getPcRedirectUri());
    }

    @Override
    public OpenAuthRequest buildAuthRequest(String code) {
        return OpenAuthRequest.builder()
                .url(GOOGLE_AUTH_URL)
                .clientId(socialPlatformProperties.getGoogle().getClientId())
                .query(String.format("code=%s&grant_type=authorization_code&redirect_uri=%s",
                        code,
                        socialPlatformProperties.getGoogle().getPcRedirectUri()))
                .basic(Base64.getEncoder().encodeToString(
                        String.format("%s:%s",
                                socialPlatformProperties.getGoogle().getClientId(),
                                socialPlatformProperties.getGoogle().getClientSecret()).getBytes()))
                .build();
    }

    @Override
    public SocialUserInfo getCurrentUser(AccessToken accessToken) throws CustomerException {
        // 使用Google API获取用户信息
        Request request = new Request.Builder()
                .url("https://www.googleapis.com/oauth2/v3/userinfo")
                .addHeader("Authorization", "Bearer " + accessToken.getAccessToken())
                .build();

        try {
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            String responseBody = response.body().string();
            JSONObject jsonObject = new JSONObject(responseBody);
            log.info("[google] currentUser:{}", jsonObject);

            String id = jsonObject.getString("sub");
            String username = jsonObject.getString("name");
            String email = jsonObject.optString("email"); // 获取email字段
            String profileImage = jsonObject.getString("picture");
            // 转换为统一的用户信息模型
            return SocialUserInfo.builder()
                    .userId(id)
                    .username(username)
                    .email(email)
                    .profileImageUrl(profileImage)
                    .build();
        } catch (Exception e) {
            log.error("Error getting Google user info", e);
            return null;
        }
    }

    @Override
    public CustomerBind validateUserBinding(SocialUserInfo userInfo) {
        if (userInfo == null) {
            return null;
        }

        // 构建查询参数，Google平台优先使用email进行验证
        CustomerBindQuery query = CustomerBindQuery.builder()
                .socialPlatform(getPlatformType())
                .socialEmail(userInfo.getEmail())
                .build();

        log.info("[GooglePlatformService] Validating user binding with query: platform={}, email={}",
            query.getSocialPlatform(), query.getSocialEmail());

        CustomerBind existingBind = customerBindService.findBySocialInfo(query);
        if (existingBind != null) {
            log.info("[GooglePlatformService] Found existing binding: {}", existingBind);
        }

        return existingBind;
    }

}
