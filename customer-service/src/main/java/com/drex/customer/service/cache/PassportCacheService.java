package com.drex.customer.service.cache;

import com.drex.customer.api.response.PassportDTO;
import com.drex.customer.service.business.PassportService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class PassportCacheService extends BaseCache<String, PassportDTO>{

    @Resource
    private PassportService passportService;

    public PassportDTO get(String passportId) {
        if (passportId == null) {
            return null;
        }
        return super.getUnchecked(passportId);
    }

    @Override
    public PassportDTO loadEntry(String s) {
        return passportService.getPassportById(s);
    }

    @Override
    protected long expireSeconds() {
        return TimeUnit.HOURS.toSeconds(3);
    }

    @Override
    public String prefix() {
        return "passport";
    }
}
