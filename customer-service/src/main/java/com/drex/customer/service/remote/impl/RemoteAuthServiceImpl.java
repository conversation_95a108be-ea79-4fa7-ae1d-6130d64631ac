package com.drex.customer.service.remote.impl;

import co.evg.scaffold.event.client.EventClient;
import co.evg.scaffold.event.client.EventDTO;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.drex.customer.api.ErrorCode;
import com.drex.customer.api.RemoteAuthService;
import com.drex.customer.api.constants.CustomerConstants;
import com.drex.customer.api.constants.WalletConstant;
import com.drex.customer.api.request.AuthLoginRequest;
import com.drex.customer.api.request.BindWalletRequest;
import com.drex.customer.api.request.CustomerBindQuery;
import com.drex.customer.api.request.ThirdAuthRequest;
import com.drex.customer.api.response.*;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.service.business.AuthService;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.service.business.PassportService;
import com.drex.customer.service.business.ThirdWebService;
import com.drex.customer.service.code.client.CustomerCodeServiceClient;
import com.drex.customer.service.config.CustomerProperties;
import com.drex.customer.service.mapstruct.AuthMapperStruct;
import com.drex.customer.service.socialPlatform.model.Token;
import com.drex.customer.service.socialPlatform.service.SocialPlatformServiceFactory;
import com.drex.model.CustomerException;
import com.kikitrade.framework.common.model.Response;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.framework.ons.OnsProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

@DubboService
@Slf4j
public class RemoteAuthServiceImpl implements RemoteAuthService {

    @Resource
    private AuthService authService;
    @Resource
    private AuthMapperStruct authMapperStruct;
    @Resource
    private CustomerBindService customerBindService;
    @Resource
    private ThirdWebService thirdWebService;
    @Resource
    private SocialPlatformServiceFactory platformServiceFactory;
    @Resource
    private CustomerCodeServiceClient customerCodeServiceClient;
    @Resource
    private PassportService passportService;
    @Resource
    private EventClient eventClient;
    @Resource
    private CustomerProperties customerProperties;
    @Resource
    private OnsProducer onsProducer;
    @Resource
    private OnsProperties onsProperties;


    @Override
    public Response<PassportDTO> login(AuthLoginRequest request) {
        try {
            log.info("login request:{}", request);
            if (StringUtils.isEmpty(request.getWalletAddress())) {
                return Response.error(ErrorCode.LOGIN_FAIL.getCode(), "walletAddress is required");
            }

            ThirdWebService.ThirdWebUserAccount thirdWebUserAccount =
                    thirdWebService.getThirdWebUserAccount(request.getWalletAddress(), request.getSubConnectProvider());
            if (thirdWebUserAccount == null) {
                request.setSubConnectProvider(WalletConstant.PlatformEnum.Wallet);
            } else {
                //如果前端送的platform不对，或者2b用户送空，则以ThirdWeb为准，取最后一条社媒信息
                if (request.getSubConnectProvider() == null || request.getSubConnectProvider() == WalletConstant.PlatformEnum.Wallet) {
                    request.setSubConnectProvider(WalletConstant.PlatformEnum.getEnumByCode(thirdWebUserAccount.getType()));
                }
            }

            log.info("thirdweb user account: {}", thirdWebUserAccount);
            PassportDTO passportDTO = passportService.getPassportByWalletAddress(request.getWalletAddress(), request.getSubConnectProvider());
            List<PassportConnectDTO> passportConnectByAddress = new ArrayList<>();
            if (passportDTO == null) {
                passportConnectByAddress = passportService.getPassportConnectByAddress(request.getWalletAddress());
            }
            if (WalletConstant.PlatformEnum.Wallet == request.getSubConnectProvider()) {
                //钱包登录
                if (!CollectionUtils.isEmpty(passportConnectByAddress)) {
                    boolean res = passportConnectByAddress.stream()
                            .anyMatch(connect -> WalletConstant.ConnectTypeEnum.BIND.getCode().equalsIgnoreCase(connect.getConnectType()));
                    if (res) {
                        //如果passport不存在，并且当前地址有BIND类型的connect记录，则不允许登录
                        return Response.error(ErrorCode.CONNECTED_BY_ANOTHER_PASSPORT.getCode(), ErrorCode.CONNECTED_BY_ANOTHER_PASSPORT.getMessage());
                    }
                }
            } else {
                //非钱包登录
                CustomerBind bindInfo;
                CustomerBindQuery query = CustomerBindQuery.builder()
                        .socialPlatform(request.getSubConnectProvider().name())
                        .socialUserId(thirdWebUserAccount.getId())
                        .socialUserName(thirdWebUserAccount.getUsername())
                        .socialEmail(thirdWebUserAccount.getEmail())
                        .build();
                bindInfo = customerBindService.findBySocialInfo(query);
                AtomicReference<PassportConnectDTO> keyConnect = new AtomicReference<>();
                if (!CollectionUtils.isEmpty(passportConnectByAddress)) {
                    // 4. 获取第一条KEY类型的连接（社媒类型EOA 的 connect，只要存在，就必然是 KEY 钱包）
                    passportConnectByAddress.stream()
                            .filter(connect -> WalletConstant.ConnectTypeEnum.KEY.getCode().equalsIgnoreCase(connect.getConnectType()))
                            .findFirst()
                            .ifPresent(connect -> {
                                keyConnect.set(connect);
                                log.info("key connect found, passportConnectDTO:{}, passportId:{}", connect, connect.getPassportId());
                            });
                }

                /*
                  这里检查了三种情况，都拒绝登录/注册
                   1. 社交账号已绑定，当前登录的社媒有passport，但是绑定的 passportId 与当前登录的 passportId 不一致
                   2. 社交账号已绑定，当前登录的社媒没有passport，使用eoa地址找到passportId，与bind表比较，passportId不一致
                   3. 社交账号已绑定，当前登录的社媒没有passport，且eoa地址在passport_connect表找不到记录
                 */
                if ((bindInfo != null && passportDTO != null && !bindInfo.getCustomerId().equals(passportDTO.getPassportId()))
                        || (bindInfo != null && passportDTO == null && keyConnect.get() != null && !keyConnect.get().getPassportId().equals(bindInfo.getCustomerId()))
                        || (bindInfo != null && passportDTO == null && keyConnect.get() == null)) {
                    return Response.error(ErrorCode.CONNECTED_BY_ANOTHER_PASSPORT.getCode(), ErrorCode.CONNECTED_BY_ANOTHER_PASSPORT.getMessage());
                }
                if (passportDTO == null && keyConnect.get() != null) {
                    //合并
                    createSocialEOAConnect(request.getWalletAddress(), keyConnect.get().getPassportId(), request.getSubConnectProvider(), thirdWebUserAccount);
                    passportDTO = passportService.getPassportByWalletAddress(request.getWalletAddress(), request.getSubConnectProvider());
                }
            }

            boolean checkCode = true;
            if (passportDTO == null) {
                if ("-1".equals(request.getCode())) {
                    return Response.error(ErrorCode.LOGIN_FAIL.getCode(), "code is invalid");
                }
                if (StringUtils.isNotBlank(request.getCode())) {
                    try {
                        //核销二维码
                        checkCode = customerCodeServiceClient.checkCode(request.getCode(), new String[]{CustomerConstants.SceneEnum.register.name()}, null);
                    } catch (Exception e) {
                        log.error("login ", e);
                        checkCode = false;
                    }
                }
                // Create new customer
                if (!checkCode) {
                    return Response.error(ErrorCode.LOGIN_FAIL.getCode(), "code is invalid");
                }
                passportDTO = register(request);
                if (passportDTO == null) {
                    return Response.error(ErrorCode.LOGIN_FAIL.getCode(), "register fail");
                }
                passportDTO.setIsNewUser(true);
            } else {
                if (StringUtils.isNotBlank(request.getCode()) && !"-1".equals(request.getCode())) {
                    try {
                        //核销二维码
                        checkCode = customerCodeServiceClient.checkCode(request.getCode(), new String[]{CustomerConstants.SceneEnum.register.name()}, passportDTO.getPassportId());
                    } catch (Exception e) {
                        log.error("login ", e);
                        checkCode = false;
                    }
                }
            }
            if (checkCode && StringUtils.isNotBlank(request.getCode()) && !"-1".equals(request.getCode())) {
                CustomerCodesDTO customerCodesDTO = customerCodeServiceClient.useCode(passportDTO, request.getCode(), new String[]{CustomerConstants.SceneEnum.register.name()}, null);
                //发送event消息
                if (customerCodesDTO != null) {
                    passportDTO.setIsRedeemedSuccessfully(true);
                    sendBadgeEvent(passportDTO.getPassportId(), customerCodesDTO.getScene(), CustomerConstants.SERIES_TREX_JOURNEY, new HashMap<>());
                }
            }
            // Create response with code, message and data structure
            Response<PassportDTO> response = Response.success(passportDTO);
            response.setMessage("success");

            return response;
        } catch (Exception e) {
            log.error("login error", e);
            return Response.error(ErrorCode.UNKNOWN_ERROR.getCode(), ErrorCode.UNKNOWN_ERROR.getMessage());
        }
    }

    private boolean checkAddressValid(String walletAddress) {
        List<PassportConnectDTO> passportConnectByAddress = passportService.getPassportConnectByAddress(walletAddress);
        if (!CollectionUtils.isEmpty(passportConnectByAddress)) {
            log.info("checkAddress walletAddress: {} has bound", walletAddress);
            return false;
        }
        return true;
    }

    private void createSocialEOAConnect(String eoaAddress, String passportId, WalletConstant.PlatformEnum subConnectProvider, ThirdWebService.ThirdWebUserAccount thirdWebUserAccount) throws Exception {
        log.info("begin to handle social eoa merge, walletAddress: {}, passportId: {}, subConnectProvider: {}", eoaAddress, passportId, subConnectProvider);
        BindWalletRequest bindRequest = BindWalletRequest.builder()
                .passportId(passportId)
                .walletAddress(eoaAddress)
                .walletProvider(WalletConstant.WalletProviderEnum.ThirdWeb)
                .subConnectProvider(subConnectProvider)
                .connectType(WalletConstant.ConnectTypeEnum.KEY)
                .walletType(WalletConstant.WalletTypeEnum.SOCIAL)
                .accountDetail(thirdWebUserAccount.getDetails())
                .build();
        Boolean b = passportService.bindWalletAddress(bindRequest);
        //注册后同步绑定社媒
        if (b && !WalletConstant.PlatformEnum.Wallet.equals(subConnectProvider)) {
            CustomerBind customerBind = new CustomerBind();
            customerBind.setCustomerId(passportId);
            customerBind.setSocialPlatform(subConnectProvider.name());
            customerBind.setSocialUserId(thirdWebUserAccount.getId());
            customerBind.setSocialHandleName(thirdWebUserAccount.getUsername());
            customerBind.setSocialEmail(thirdWebUserAccount.getEmail());
            customerBind.setCreated(System.currentTimeMillis());
            customerBindService.insert(customerBind);
        }
        log.info("handle social eoa merge result: {}", b);
    }

    @Override
    public Response<ThirdAuthDTO> thirdAuth(ThirdAuthRequest request) {
        try {
            Token token = authService.auth(request.getPlatform(), request.getCode(), request.getCustomerId());
            ThirdAuthDTO thirdAuthResponse = authMapperStruct.toThirdAuthDTO(token);
            return Response.success(thirdAuthResponse);
        } catch (CustomerException e) {
            return Response.error(e.getCode().getCode(), e.getCode().getMessage());
        }
    }

    @Override
    public Response<List<ThirdBindingsDTO>> thirdBindings(String customerId) {
        log.info("[RemoteAuthService] thirdBindings customerId: {}", customerId);
        // 获取支持的社交平台列表
        List<String> socialPlatforms = platformServiceFactory.getSupportedPlatforms();

        // 查询用户已绑定的社交平台
        List<CustomerBind> customerBinds = customerBindService.findByCustomerId(customerId, socialPlatforms);

        // 创建结果列表
        List<ThirdBindingsDTO> result = new ArrayList<>();

        // 处理每个社交平台
        for (String platform : socialPlatforms) {
            ThirdBindingsDTO bindingsDTO = new ThirdBindingsDTO();
            bindingsDTO.setPlatform(platform);

            // 查找当前平台是否已绑定
            CustomerBind existingBind = customerBinds.stream()
                    .filter(bind -> platform.equals(bind.getSocialPlatform()))
                    .filter(bind -> isValidBinding(platform, bind))
                    .findFirst()
                    .orElse(null);

            if (existingBind != null) {
                // 已绑定
                bindingsDTO.setStatus("1");
                bindingsDTO.setConnectUrl(null);
                bindingsDTO.setSocialHandleName(existingBind.getSocialHandleName());
                bindingsDTO.setSocialProfileImage(existingBind.getSocialProfileImage());
            } else {
                // 未绑定
                bindingsDTO.setStatus("0");
                // 配置的平台特殊处理：未绑定时不展示
                if (customerProperties.getSocialPlatformSkipList().contains(platform)) {
                    continue;
                }
            }
            result.add(bindingsDTO);
        }
        return Response.success(result);
    }

    /**
     * 校验绑定是否有效
     * Email和Google平台需要校验socialEmail不为空才认为已绑定
     * 其他平台只校验socialUserId不为空即可
     *
     * @param platform 平台类型
     * @param bind     绑定记录
     * @return 是否为有效绑定
     */
    private boolean isValidBinding(String platform, CustomerBind bind) {
        if (bind == null) {
            return false;
        }

        // Email和Google平台需要校验socialEmail不为空
        if (WalletConstant.PlatformEnum.Email.name().equalsIgnoreCase(platform) ||
                WalletConstant.PlatformEnum.Google.name().equalsIgnoreCase(platform)) {
            return StringUtils.isNotBlank(bind.getSocialEmail());
        }

        // 其他平台只校验socialUserId不为空
        return StringUtils.isNotBlank(bind.getSocialUserId());
    }

    private PassportDTO register(AuthLoginRequest request) {
        PassportDTO passport = passportService.createPassport(request);
        if (passport != null) {
            Map<String, String> body = new HashMap<>();
            body.put("event", "register");
            body.put("data", JSON.toJSONString(passport));
            onsProducer.send(customerProperties.getCustomerRegisterTopic() + onsProperties.getEnv(), JSON.toJSONString(body));

            // 发送早鸟徽章
            body.put("progressValue", passport.getCreatedAt().getTime() + "");
            sendBadgeEvent(passport.getPassportId(), CustomerConstants.SCENE_TREX_OG, CustomerConstants.SERIES_TREX_JOURNEY, body);
        }
        return passport;
    }

    private void sendBadgeEvent(String customerId, String scene, String series, Map<String, String> params) {
        JSONObject body = new JSONObject();
        if (params != null) {
            body.putAll(params);
        }
        body.put("customerId", customerId);
        body.put("contentId", customerId + "_" + scene);
        body.put("series", series);
        body.put("scene", scene);

        EventDTO event = EventDTO.builder()
                .name("claimable_badge")
                .time(System.currentTimeMillis())
                .customerId(customerId)
                .globalUid(body.getString("contentId"))
                .source(eventClient.getDefaultSource())
                .body(body)
                .build();
        eventClient.asyncPush(event);
    }
}
