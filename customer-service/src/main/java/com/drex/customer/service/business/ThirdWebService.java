package com.drex.customer.service.business;

import com.drex.customer.api.constants.WalletConstant;
import lombok.Data;

public interface ThirdWebService {

    ThirdWebUserAccount getThirdWebUserAccount(String walletAddress, WalletConstant.PlatformEnum platformEnum);

    @Data
    class ThirdWebUserAccount {
        private String address;
        private String type;
        private String id;
        private String username;
        private String email;
        private String details;
    }
}


