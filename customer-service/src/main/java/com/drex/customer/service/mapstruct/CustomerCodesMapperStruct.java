package com.drex.customer.service.mapstruct;

import com.drex.customer.api.response.CustomerCodesDTO;
import com.drex.customer.dal.tablestore.model.CustomerCodeTransaction;
import com.drex.customer.dal.tablestore.model.CustomerCodes;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CustomerCodesMapperStruct {

    CustomerCodesDTO toCustomerCodesDTO(CustomerCodeTransaction customerCodeTransaction);
}
