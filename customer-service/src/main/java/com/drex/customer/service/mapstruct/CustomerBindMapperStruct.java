package com.drex.customer.service.mapstruct;

import com.drex.customer.api.response.CustomerBindDTO;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2025/4/28 11:11
 * @description:
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CustomerBindMapperStruct {

    CustomerBindDTO toCustomerBindDTO(CustomerBind customerBind);

}
