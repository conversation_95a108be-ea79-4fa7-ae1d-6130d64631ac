package com.drex.customer.service.code.client;

import com.alibaba.fastjson.JSONObject;
import com.drex.customer.api.request.CustomerCodeGenerateRequest;
import com.drex.customer.api.response.CustomerCodesDTO;
import com.drex.customer.api.response.CustomerDTO;
import com.drex.customer.api.response.PassportDTO;
import com.drex.customer.dal.tablestore.model.Customer;
import com.drex.model.CustomerException;

/**
 * @ClassName CustomerCodeServiceClient
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/20 19:14
 **/
public interface CustomerCodeServiceClient {

    // 生成code
    public String generateCode(CustomerCodeGenerateRequest request) throws CustomerException;

    // 检查code是否可用 需要上送场景
    public Boolean checkCode(String code, String[] scene, String customerId) throws CustomerException;

    public CustomerCodesDTO useCode(PassportDTO passportDTO, String code, String[] scene, JSONObject attribute) throws CustomerException;
}
