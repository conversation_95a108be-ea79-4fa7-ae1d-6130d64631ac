package com.drex.customer.service.business;

import java.util.List;

/**
 * 社媒信息同步服务
 * <AUTHOR>
 */
public interface SocialSyncService {

    /**
     * 同步所有passport的社媒信息到customerBind表
     * @return 同步结果统计信息
     */
    SyncResult syncAllPassportSocialInfo();

    /**
     * 重置customer_bind表中所有记录的privacyAuth字段为null
     * 遍历所有数据，将privacyAuth字段置为null，然后先删除再插入
     */
    void resetPrivacyAuth();

    /**
     * 同步结果统计
     */
    class SyncResult {
        private int totalProcessed;
        private int successCount;
        private int skipCount;
        private int errorCount;
        private List<String> failedPassportIds;
        private String message;

        public SyncResult() {}

        public SyncResult(int totalProcessed, int successCount, int skipCount, int errorCount,
                         List<String> failedPassportIds, String message) {
            this.totalProcessed = totalProcessed;
            this.successCount = successCount;
            this.skipCount = skipCount;
            this.errorCount = errorCount;
            this.failedPassportIds = failedPassportIds;
            this.message = message;
        }

        // Getters and Setters
        public int getTotalProcessed() { return totalProcessed; }
        public void setTotalProcessed(int totalProcessed) { this.totalProcessed = totalProcessed; }

        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }

        public int getSkipCount() { return skipCount; }
        public void setSkipCount(int skipCount) { this.skipCount = skipCount; }

        public int getErrorCount() { return errorCount; }
        public void setErrorCount(int errorCount) { this.errorCount = errorCount; }

        public List<String> getFailedPassportIds() { return failedPassportIds; }
        public void setFailedPassportIds(List<String> failedPassportIds) { this.failedPassportIds = failedPassportIds; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }

        @Override
        public String toString() {
            return String.format("SyncResult{totalProcessed=%d, successCount=%d, skipCount=%d, errorCount=%d, failedPassportIds=%s, message='%s'}",
                    totalProcessed, successCount, skipCount, errorCount, failedPassportIds, message);
        }
    }
}
