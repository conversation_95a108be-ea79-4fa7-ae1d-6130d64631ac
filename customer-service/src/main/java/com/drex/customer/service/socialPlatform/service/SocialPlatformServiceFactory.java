package com.drex.customer.service.socialPlatform.service;

import com.drex.customer.api.constants.WalletConstant;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 社交平台服务工厂
 * 管理所有社交平台服务实现
 * <AUTHOR>
 */
@Component
@Slf4j
public class SocialPlatformServiceFactory {

    @Autowired
    private List<SocialPlatformService> platformServices;

    private final Map<String, SocialPlatformService> serviceMap = new HashMap<>();

    @PostConstruct
    public void init() {
        platformServices.forEach(service -> {
            serviceMap.put(service.getPlatformType(), service);
            log.info("Registered social platform service: {}", service.getPlatformType());
        });
    }

    /**
     * 获取指定平台的服务
     * @param platform 平台类型
     * @return 平台服务实现
     */
    public SocialPlatformService getService(String platform) {
        SocialPlatformService service = serviceMap.get(platform);
        if (service == null) {
            log.warn("No service found for platform: {}", platform);
        }
        return service;
    }

    /**
     * 获取所有支持的平台类型
     * @return 平台类型列表
     */
    public List<String> getSupportedPlatforms() {
        return List.of(
                WalletConstant.PlatformEnum.Google.name(),
                WalletConstant.PlatformEnum.X.name(),
                WalletConstant.PlatformEnum.Discord.name(),
                WalletConstant.PlatformEnum.Email.name(),
                WalletConstant.PlatformEnum.Telegram.name(),
                WalletConstant.PlatformEnum.Apple.name(),
                WalletConstant.PlatformEnum.Facebook.name(),
                WalletConstant.PlatformEnum.Line.name()
        );
    }

    /**
     * 获取所有需要手动绑定的平台类型
     * @return 平台类型列表
     */
    public static List<String> getNeedManualBindPlatforms() {
        return List.of(
                WalletConstant.PlatformEnum.Google.name(),
                WalletConstant.PlatformEnum.X.name(),
                WalletConstant.PlatformEnum.Discord.name(),
                WalletConstant.PlatformEnum.Email.name(),
                WalletConstant.PlatformEnum.Telegram.name(),
                WalletConstant.PlatformEnum.Facebook.name()
        );
    }
}
