package com.drex.customer.service.message;

import com.alibaba.fastjson2.JSON;
import com.drex.customer.service.config.CustomerProperties;
import com.drex.model.ActivityEventMessage;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.framework.ons.OnsProperties;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 延迟消息发送服务
 * 用于延迟发送活动事件消息
 * <AUTHOR>
 */
@Service
@Slf4j
public class DelayedMessageService {
    
    @Resource
    private OnsProducer onsProducer;
    
    @Resource
    private OnsProperties onsProperties;
    
    @Resource
    private CustomerProperties customerProperties;
    
    private ScheduledExecutorService scheduledExecutorService;

    private static final int corePoolSize = 2 * Runtime.getRuntime().availableProcessors();

    @PostConstruct
    public void init() {
        // 初始化线程池
        scheduledExecutorService = Executors.newScheduledThreadPool(
                corePoolSize,
                r -> {
                    Thread thread = new Thread(r, "delayed-message-thread");
                    thread.setDaemon(true);
                    return thread;
                }
        );
        log.info("DelayedMessageService initialized with thread pool size: {}", 
                corePoolSize);
    }
    
    @PreDestroy
    public void destroy() {
        if (scheduledExecutorService != null && !scheduledExecutorService.isShutdown()) {
            scheduledExecutorService.shutdown();
            try {
                if (!scheduledExecutorService.awaitTermination(30, TimeUnit.SECONDS)) {
                    scheduledExecutorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduledExecutorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
            log.info("DelayedMessageService shutdown completed");
        }
    }
    
    /**
     * 延迟发送活动事件消息
     * @param message 活动事件消息
     */
    public void sendDelayedActivityEvent(ActivityEventMessage message) {
        int delaySeconds = customerProperties.getActivityEventDelaySeconds();
        
        log.info("Scheduling delayed activity event message: eventCode={}, customerId={}, delay={}s", 
                message.getEventCode(), message.getCustomerId(), delaySeconds);
        
        scheduledExecutorService.schedule(() -> {
            try {
                String topic = customerProperties.getActivityEventTopic() + onsProperties.getEnv();
                String messageBody = JSON.toJSONString(message);
                
                onsProducer.send(topic, messageBody);
                
                log.info("Delayed activity event message sent successfully: eventCode={}, customerId={}", 
                        message.getEventCode(), message.getCustomerId());
            } catch (Exception e) {
                log.error("Failed to send delayed activity event message: eventCode={}, customerId={}", 
                        message.getEventCode(), message.getCustomerId(), e);
            }
        }, delaySeconds, TimeUnit.SECONDS);
    }
    
    /**
     * 立即发送活动事件消息（不延迟）
     * @param message 活动事件消息
     */
    public void sendImmediateActivityEvent(ActivityEventMessage message) {
        try {
            String topic = customerProperties.getActivityEventTopic() + onsProperties.getEnv();
            String messageBody = JSON.toJSONString(message);
            
            onsProducer.send(topic, messageBody);
            
            log.info("Immediate activity event message sent successfully: eventCode={}, customerId={}", 
                    message.getEventCode(), message.getCustomerId());
        } catch (Exception e) {
            log.error("Failed to send immediate activity event message: eventCode={}, customerId={}", 
                    message.getEventCode(), message.getCustomerId(), e);
        }
    }
    
    /**
     * 获取当前线程池状态信息
     * @return 线程池状态信息
     */
    public String getThreadPoolStatus() {
        if (scheduledExecutorService == null) {
            return "Thread pool not initialized";
        }
        
        return String.format("Thread pool status - isShutdown: %s, isTerminated: %s", 
                scheduledExecutorService.isShutdown(), 
                scheduledExecutorService.isTerminated());
    }
}
