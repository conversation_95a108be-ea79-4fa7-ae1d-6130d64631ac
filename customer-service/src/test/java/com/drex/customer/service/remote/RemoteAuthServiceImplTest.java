package com.drex.customer.service.remote;

import com.drex.customer.api.response.ThirdBindingsDTO;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.service.config.CustomerProperties;
import com.drex.customer.service.remote.impl.RemoteAuthServiceImpl;
import com.drex.customer.service.socialPlatform.service.SocialPlatformServiceFactory;
import com.kikitrade.framework.common.model.Response;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * RemoteAuthServiceImpl 测试类
 * 主要测试社交平台绑定功能中的配置驱动跳过逻辑
 */
public class RemoteAuthServiceImplTest {

    @Mock
    private SocialPlatformServiceFactory platformServiceFactory;

    @Mock
    private CustomerBindService customerBindService;

    @Mock
    private CustomerProperties customerProperties;

    @InjectMocks
    private RemoteAuthServiceImpl remoteAuthService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testThirdBindings_WithDefaultSkipList_ShouldSkipAppleAndLine() {
        // 准备测试数据
        String customerId = "test-customer-id";
        
        // Mock 支持的平台列表
        List<String> supportedPlatforms = Arrays.asList(
                "Google", "X", "Discord", "Email", "Telegram", "Apple", "Facebook", "Line"
        );
        when(platformServiceFactory.getSupportedPlatforms()).thenReturn(supportedPlatforms);
        
        // Mock 用户没有绑定任何平台
        when(customerBindService.findByCustomerId(anyString(), anyList())).thenReturn(Collections.emptyList());
        
        // Mock 配置的跳过列表（默认包含Apple和Line）
        when(customerProperties.getSocialPlatformSkipList()).thenReturn(Arrays.asList("Apple", "Line"));

        // 执行测试
        Response<List<ThirdBindingsDTO>> response = remoteAuthService.thirdBindings(customerId);

        // 验证结果
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        
        List<ThirdBindingsDTO> bindings = response.getData();
        
        // 验证Apple和Line平台被跳过，不在结果中
        List<String> resultPlatforms = bindings.stream()
                .map(ThirdBindingsDTO::getPlatform)
                .toList();
        
        assertFalse(resultPlatforms.contains("Apple"), "Apple平台应该被跳过");
        assertFalse(resultPlatforms.contains("Line"), "Line平台应该被跳过");
        
        // 验证其他平台存在
        assertTrue(resultPlatforms.contains("Google"), "Google平台应该存在");
        assertTrue(resultPlatforms.contains("X"), "X平台应该存在");
        assertTrue(resultPlatforms.contains("Discord"), "Discord平台应该存在");
        assertTrue(resultPlatforms.contains("Email"), "Email平台应该存在");
        assertTrue(resultPlatforms.contains("Telegram"), "Telegram平台应该存在");
        assertTrue(resultPlatforms.contains("Facebook"), "Facebook平台应该存在");
        
        // 验证所有返回的平台状态都是未绑定
        bindings.forEach(binding -> {
            assertEquals("0", binding.getStatus(), "未绑定平台状态应该为0");
        });
    }

    @Test
    void testThirdBindings_WithCustomSkipList_ShouldSkipConfiguredPlatforms() {
        // 准备测试数据
        String customerId = "test-customer-id";
        
        // Mock 支持的平台列表
        List<String> supportedPlatforms = Arrays.asList(
                "Google", "X", "Discord", "Email", "Telegram", "Apple", "Facebook", "Line"
        );
        when(platformServiceFactory.getSupportedPlatforms()).thenReturn(supportedPlatforms);
        
        // Mock 用户没有绑定任何平台
        when(customerBindService.findByCustomerId(anyString(), anyList())).thenReturn(Collections.emptyList());
        
        // Mock 自定义的跳过列表（只跳过Google和Discord）
        when(customerProperties.getSocialPlatformSkipList()).thenReturn(Arrays.asList("Google", "Discord"));

        // 执行测试
        Response<List<ThirdBindingsDTO>> response = remoteAuthService.thirdBindings(customerId);

        // 验证结果
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        
        List<ThirdBindingsDTO> bindings = response.getData();
        
        // 验证Google和Discord平台被跳过
        List<String> resultPlatforms = bindings.stream()
                .map(ThirdBindingsDTO::getPlatform)
                .toList();
        
        assertFalse(resultPlatforms.contains("Google"), "Google平台应该被跳过");
        assertFalse(resultPlatforms.contains("Discord"), "Discord平台应该被跳过");
        
        // 验证Apple和Line现在不会被跳过
        assertTrue(resultPlatforms.contains("Apple"), "Apple平台不应该被跳过");
        assertTrue(resultPlatforms.contains("Line"), "Line平台不应该被跳过");
        
        // 验证其他平台存在
        assertTrue(resultPlatforms.contains("X"), "X平台应该存在");
        assertTrue(resultPlatforms.contains("Email"), "Email平台应该存在");
        assertTrue(resultPlatforms.contains("Telegram"), "Telegram平台应该存在");
        assertTrue(resultPlatforms.contains("Facebook"), "Facebook平台应该存在");
    }

    @Test
    void testThirdBindings_WithEmptySkipList_ShouldNotSkipAnyPlatform() {
        // 准备测试数据
        String customerId = "test-customer-id";
        
        // Mock 支持的平台列表
        List<String> supportedPlatforms = Arrays.asList(
                "Google", "X", "Discord", "Email", "Telegram", "Apple", "Facebook", "Line"
        );
        when(platformServiceFactory.getSupportedPlatforms()).thenReturn(supportedPlatforms);
        
        // Mock 用户没有绑定任何平台
        when(customerBindService.findByCustomerId(anyString(), anyList())).thenReturn(Collections.emptyList());
        
        // Mock 空的跳过列表
        when(customerProperties.getSocialPlatformSkipList()).thenReturn(Collections.emptyList());

        // 执行测试
        Response<List<ThirdBindingsDTO>> response = remoteAuthService.thirdBindings(customerId);

        // 验证结果
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        
        List<ThirdBindingsDTO> bindings = response.getData();
        
        // 验证所有平台都存在，没有被跳过
        List<String> resultPlatforms = bindings.stream()
                .map(ThirdBindingsDTO::getPlatform)
                .toList();
        
        assertEquals(supportedPlatforms.size(), resultPlatforms.size(), "所有平台都应该存在");
        
        for (String platform : supportedPlatforms) {
            assertTrue(resultPlatforms.contains(platform), platform + "平台应该存在");
        }
    }

    @Test
    void testThirdBindings_WithBoundPlatform_ShouldNotSkipBoundPlatforms() {
        // 准备测试数据
        String customerId = "test-customer-id";
        
        // Mock 支持的平台列表
        List<String> supportedPlatforms = Arrays.asList(
                "Google", "X", "Discord", "Email", "Telegram", "Apple", "Facebook", "Line"
        );
        when(platformServiceFactory.getSupportedPlatforms()).thenReturn(supportedPlatforms);
        
        // Mock 用户已绑定Apple平台
        CustomerBind appleBind = new CustomerBind();
        appleBind.setSocialPlatform("Apple");
        appleBind.setSocialUserId("apple-user-id");
        appleBind.setSocialHandleName("apple-user");
        appleBind.setSocialEmail("<EMAIL>");
        
        when(customerBindService.findByCustomerId(anyString(), anyList())).thenReturn(Arrays.asList(appleBind));
        
        // Mock 配置的跳过列表（包含Apple和Line）
        when(customerProperties.getSocialPlatformSkipList()).thenReturn(Arrays.asList("Apple", "Line"));

        // 执行测试
        Response<List<ThirdBindingsDTO>> response = remoteAuthService.thirdBindings(customerId);

        // 验证结果
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        
        List<ThirdBindingsDTO> bindings = response.getData();
        
        // 验证已绑定的Apple平台不会被跳过
        List<String> resultPlatforms = bindings.stream()
                .map(ThirdBindingsDTO::getPlatform)
                .toList();
        
        assertTrue(resultPlatforms.contains("Apple"), "已绑定的Apple平台不应该被跳过");
        assertFalse(resultPlatforms.contains("Line"), "未绑定的Line平台应该被跳过");
        
        // 验证Apple平台的绑定状态
        ThirdBindingsDTO appleBinding = bindings.stream()
                .filter(binding -> "Apple".equals(binding.getPlatform()))
                .findFirst()
                .orElse(null);
        
        assertNotNull(appleBinding, "Apple绑定信息应该存在");
        assertEquals("1", appleBinding.getStatus(), "Apple平台应该显示为已绑定");
        assertEquals("apple-user", appleBinding.getSocialHandleName(), "Apple用户名应该正确");
    }
}
