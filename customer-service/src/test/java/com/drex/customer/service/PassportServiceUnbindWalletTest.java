package com.drex.customer.service;

import com.drex.customer.api.constants.WalletConstant;
import com.drex.customer.api.request.UnbindWalletRequest;
import com.drex.customer.dal.tablestore.builder.PassportConnectBuilder;
import com.drex.customer.dal.tablestore.model.PassportConnect;
import com.drex.customer.service.business.impl.PassportServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 解绑钱包功能测试
 */
public class PassportServiceUnbindWalletTest {

    @Mock
    private PassportConnectBuilder passportConnectBuilder;

    @InjectMocks
    private PassportServiceImpl passportService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testUnbindWalletSuccess() {
        // 准备测试数据
        String passportId = "test-passport-id";
        String walletAddress = "0x1234567890abcdef";
        
        UnbindWalletRequest request = UnbindWalletRequest.builder()
                .passportId(passportId)
                .walletAddress(walletAddress)
                .build();

        PassportConnect passportConnect = new PassportConnect();
        passportConnect.setPassportId(passportId);
        passportConnect.setWalletAddress(walletAddress);
        passportConnect.setConnectType("BIND");
        passportConnect.setStatus(WalletConstant.PassportConnectStatus.ACTIVE.getCode());
        passportConnect.setConnectedAt(new Date());

        // Mock 方法调用
        when(passportConnectBuilder.getByPassportIdAndWalletAddress(passportId, walletAddress))
                .thenReturn(passportConnect);
        when(passportConnectBuilder.updatePassportConnect(any(PassportConnect.class)))
                .thenReturn(true);

        // 执行测试
        Boolean result = passportService.unbindWalletAddress(request);

        // 验证结果
        assertTrue(result);
        verify(passportConnectBuilder).getByPassportIdAndWalletAddress(passportId, walletAddress);
        verify(passportConnectBuilder).updatePassportConnect(any(PassportConnect.class));
    }

    @Test
    void testUnbindWalletNotFound() {
        // 准备测试数据
        String passportId = "test-passport-id";
        String walletAddress = "0x1234567890abcdef";
        
        UnbindWalletRequest request = UnbindWalletRequest.builder()
                .passportId(passportId)
                .walletAddress(walletAddress)
                .build();

        // Mock 方法调用 - 返回null表示未找到记录
        when(passportConnectBuilder.getByPassportIdAndWalletAddress(passportId, walletAddress))
                .thenReturn(null);

        // 执行测试
        Boolean result = passportService.unbindWalletAddress(request);

        // 验证结果
        assertFalse(result);
        verify(passportConnectBuilder).getByPassportIdAndWalletAddress(passportId, walletAddress);
        verify(passportConnectBuilder, never()).updatePassportConnect(any(PassportConnect.class));
    }

    @Test
    void testUnbindWalletWrongConnectType() {
        // 准备测试数据
        String passportId = "test-passport-id";
        String walletAddress = "0x1234567890abcdef";
        
        UnbindWalletRequest request = UnbindWalletRequest.builder()
                .passportId(passportId)
                .walletAddress(walletAddress)
                .build();

        PassportConnect passportConnect = new PassportConnect();
        passportConnect.setPassportId(passportId);
        passportConnect.setWalletAddress(walletAddress);
        passportConnect.setConnectType("KEY"); // 不是BIND类型
        passportConnect.setStatus(WalletConstant.PassportConnectStatus.ACTIVE.getCode());

        // Mock 方法调用
        when(passportConnectBuilder.getByPassportIdAndWalletAddress(passportId, walletAddress))
                .thenReturn(passportConnect);

        // 执行测试
        Boolean result = passportService.unbindWalletAddress(request);

        // 验证结果
        assertFalse(result);
        verify(passportConnectBuilder).getByPassportIdAndWalletAddress(passportId, walletAddress);
        verify(passportConnectBuilder, never()).updatePassportConnect(any(PassportConnect.class));
    }
}
