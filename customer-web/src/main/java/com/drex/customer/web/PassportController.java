package com.drex.customer.web;

import com.drex.customer.api.RemotePassportService;
import com.drex.customer.api.request.UnbindWalletRequest;
import com.drex.customer.service.business.SocialSyncService;
import com.kikitrade.framework.common.model.Response;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

/**
 * Passport相关的REST接口控制器
 */
@RestController
@RequestMapping("/passport")
public class PassportController {

    private static final Logger log = LoggerFactory.getLogger(PassportController.class);

    @Resource
    private RemotePassportService remotePassportService;

    @Resource
    private SocialSyncService socialSyncService;

    /**
     * 解绑钱包接口
     * 
     * @param request 解绑钱包请求
     * @return 解绑结果
     */
    @PostMapping("/unbind-wallet")
    public Response<Boolean> unbindWallet(@RequestBody UnbindWalletRequest request) {
        log.info("Received unbind wallet request: passportId={}, walletAddress={}", 
                request.getPassportId(), request.getWalletAddress());
        
        try {
            return remotePassportService.unbindWallet(request);
        } catch (Exception e) {
            log.error("Failed to unbind wallet", e);
            return Response.error("UNKNOWN_ERROR", "解绑钱包失败");
        }
    }

    /**
     * 同步所有passport的社媒信息到customerBind表
     * 查询passport表中的所有记录，调用ThirdWebService.getThirdWebUserAccount方法查询社媒信息，
     * 然后插入到customerBind表中，如果已存在则忽略。控制查询和插入频率。
     */
    @PostMapping("/sync-social-info")
    public Response<SocialSyncService.SyncResult> syncSocialInfo() {
        log.info("开始同步所有passport的社媒信息");

        try {
            SocialSyncService.SyncResult result = socialSyncService.syncAllPassportSocialInfo();
            log.info("社媒信息同步任务提交完成: {}", result);
            return Response.success(result);
        } catch (Exception e) {
            log.error("同步社媒信息失败", e);
            return Response.error("SYNC_ERROR", "同步社媒信息失败: " + e.getMessage());
        }
    }

    /**
     * 将customer_bind表中的privacy_auth字段置为null
     */
    @PostMapping("/reset-privacy-auth")
    public Response<Boolean> resetPrivacyAuth() {
        log.info("开始重置customer_bind表中的privacy_auth字段");

        try {
//            socialSyncService.resetPrivacyAuth();
            return Response.success(true);
        } catch (Exception e) {
            log.error("重置privacy_auth失败", e);
            return Response.error("RESET_ERROR", "重置privacy_auth失败: " + e.getMessage());
        }
    }


}
