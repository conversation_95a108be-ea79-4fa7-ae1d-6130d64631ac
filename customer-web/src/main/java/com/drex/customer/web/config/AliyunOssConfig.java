package com.drex.customer.web.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/26 16:28
 */
@Configuration
@EnableConfigurationProperties(AliyunOssProperties.class)
public class AliyunOssConfig {

    @Bean(name = "uploadAliyunOssUtil")
    public UploadAliyunOssUtil uploadAliyunOssUtil(AliyunOssProperties aliyunOssProperties){
        return new UploadAliyunOssUtil(aliyunOssProperties.getEndpoint(), aliyunOssProperties.getBucketName(), aliyunOssProperties.getRoleName());
    }
}
