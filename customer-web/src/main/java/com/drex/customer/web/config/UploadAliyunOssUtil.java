
package com.drex.customer.web.config;

import com.aliyun.credentials.models.Config;
import com.aliyun.credentials.models.CredentialModel;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.Credentials;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.common.auth.DefaultCredentials;
import com.aliyun.oss.model.PutObjectResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.Map;

@Slf4j
public class UploadAliyunOssUtil {

    private OSS ossClient;

    private String endpoint;
    private String bucketName;

    public UploadAliyunOssUtil() {
    }

    public UploadAliyunOssUtil(String endpoint, String bucketName, String roleName) {

        this.endpoint = endpoint;
        this.bucketName = bucketName;

        Config ossConfig = Config.build(Map.of("type", "ecs_ram_role", "roleName", roleName));

        final com.aliyun.credentials.Client credentialsClient = new com.aliyun.credentials.Client(ossConfig);

        CredentialsProvider credentialsProvider = new CredentialsProvider(){
            @Override
            public void setCredentials(Credentials credentials) {
            }

            @Override
            public Credentials getCredentials() {
                CredentialModel credential = credentialsClient.getCredential();
                return  new DefaultCredentials(credential.getAccessKeyId(), credential.getAccessKeySecret(), credential.getSecurityToken());
            }
        };
        ossClient = new OSSClientBuilder().build(endpoint, credentialsProvider);
    }

    /**
     * Upload file
     *
     * @param key
     * @param file
     */
    public PutObjectResult putObject(String path, String key, File file) {
        return ossClient.putObject(bucketName, absoluteKey(path, key), file);
    }

    public String absoluteKey(String path, String key) {

        if (StringUtils.isEmpty(path)) {
            return key;
        }

        return path + File.separator + key;
    }

    /**
     * Shutdown
     */
    public void shutdown() {

        if (ossClient == null) {
            return;
        }

        ossClient.shutdown();
    }

    public OSS getOssClient() {
        return ossClient;
    }

    public String getEndpoint() {
        return endpoint;
    }

    public String getBucketName() {
        return bucketName;
    }

    public String getLocation(String path, String filePath) {
        if(StringUtils.isBlank(filePath)){
            return null;
        }
        String p = "https://";
        return String.format("https://%s.%s/%s/%s", bucketName, endpoint.substring(p.length() - 1), path, filePath);
    }
}
