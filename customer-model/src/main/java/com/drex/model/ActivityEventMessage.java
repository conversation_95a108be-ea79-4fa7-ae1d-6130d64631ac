package com.drex.model;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/22 20:11
 * @description:
 */
@Data
@Builder
public class ActivityEventMessage implements Serializable {
    /**
     * 事件code
     */
    private String eventCode;

    /**
     * 用户id
     */
    private String customerId;

    /**
     * 事件发生事件
     */
    private Long eventTime;

    private Map<String, Object> body;

    private Integer inc;
}
