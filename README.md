# drex-customer 项目设计文档

## 项目概述
drex-customer 是一个客户服务系统，主要负责客户认证、社交平台集成等核心功能。

## 项目结构
- customer-api: 提供API接口定义，Dubbo服务接口、请求/响应DTO、API契约
- customer-dal: 数据访问层，数据库操作与实体映射
- customer-model: 数据模型定义，领域对象、枚举与常量
- customer-service: 核心业务逻辑实现，依赖customer-dal和customer-model，Dubbo服务实现
- customer-web: Web层实现，REST API入口，调用customer-service

### 各模块职责

#### customer-api
- 定义Dubbo服务接口
- 包含请求/响应DTO
- 作为所有消费者的共享API契约

#### customer-service
- 实现核心业务逻辑
- 依赖customer-dal进行数据访问
- 使用customer-model进行数据定义
- 实现所有Dubbo服务

#### customer-dal
- 数据访问层实现
- 数据库操作与查询
- 表/实体映射

#### customer-model
- 数据模型定义
- 共享领域对象
- 枚举与常量

#### customer-web
- Web层实现
- REST API入口
- 调用customer-service实现业务逻辑

## Dubbo服务与功能

### RemoteAuthService
负责认证相关操作：
- `Response<CustomerDTO> login(AuthLoginRequest request)`：处理客户登录
- `Response<ThirdAuthDTO> thirdAuth(ThirdAuthRequest request)`：处理第三方平台认证
- `Response<List<ThirdBindingsDTO>> thirdBindings(String customerId)`：获取客户的第三方绑定信息

### RemoteCustomerBindService
负责客户绑定相关操作：
- `CustomerBindDTO findByCustomerId(String customerId, String socialPlatform)`：根据客户ID和平台查找绑定信息
- `List<CustomerBindDTO> findByCustomerId(String customerId, List<String> socialPlatforms)`：查找多个平台的绑定信息
- `boolean unbindSocial(String customerId, String socialPlatform)`：解绑社交平台
- `boolean bindWallet(String customerId, String walletAddress)`：绑定钱包地址

### RemoteCustomerService
负责客户核心操作：
- `Response<CustomerDTO> getById(String customerId)`：根据ID获取客户信息
- `Response bindInviteCode(String customerId, String inviteCode)`：绑定邀请码
- `Response<Long> countByReferrerId(String customerId)`：统计推荐人数
- `Response<Boolean> updateLevel(String customerId, String kycLevel)`：更新KYC等级
- `Response<Boolean> addWaitList(AddWaitListRequest addWaitListRequest)`：添加到候补名单

## Documentation

- [API Documentation](./API.md) - 详细Dubbo接口说明
- [Project Modules](./MODULES.md) - 各模块职责与关系

## 技术栈
- Spring Boot
- 阿里云TableStore
- OkHttp
- Lombok

## 部署说明
1. 安装依赖: `mvn install`
2. 运行: `mvn spring-boot:run`
