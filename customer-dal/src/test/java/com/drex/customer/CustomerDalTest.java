package com.drex.customer;

import com.alibaba.fastjson.JSON;
import com.drex.customer.dal.tablestore.builder.CustomerBindBuilder;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.kikitrade.framework.common.model.TokenPage;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/28 18:36
 * @description:
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = CustomerDalTest.class)
@TestPropertySource(locations = "classpath:application.properties")
@SpringBootApplication(scanBasePackages = {"com.drex.customer.dal"})
public class CustomerDalTest {

    @Resource
    private CustomerBindBuilder customerBindBuilder;

    @Test
    void contextLoads() {}

    @Test
    public void clearPrivacyAuth() {
        List<CustomerBind> byCustomerId = customerBindBuilder.findByCustomerId("1940763595763822592");
        //循环遍历列表，将customerBind对象中的privacyAuth字段去掉，再保存
        byCustomerId.forEach(customerBind -> {
            customerBind.setPrivacyAuth(null);
            customerBindBuilder.delete(customerBind);
            customerBindBuilder.insert(customerBind);
        });
//        TokenPage<CustomerBindDO> res = customerBindBuilder.insert("monster", null, 10);
    }
}
