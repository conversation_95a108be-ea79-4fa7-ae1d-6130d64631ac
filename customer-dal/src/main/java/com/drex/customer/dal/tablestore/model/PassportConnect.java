package com.drex.customer.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.Index;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "passport_connect")
public class PassportConnect implements Serializable {

    public static final String TABLE_NAME  = "passport_connect";
    public static final String INDEX_IDENTIFIER = "idx_identifier";

    @PartitionKey(name = "identifier", value = 0)
    @Index(name = INDEX_IDENTIFIER, pkColumn = "identifier", pkValue = 1)
    private String identifier;

    @PartitionKey(name = "passport_id", value = 2)
    @Index(name = INDEX_IDENTIFIER, pkColumn = "passport_id", pkValue = 0)
    private String passportId;

    @Column(name = "connect_provider", type = Column.Type.STRING, isDefined = true)
    @Index(name = INDEX_IDENTIFIER, dfColumn = "connect_provider")
    private String connectProvider;

    @PartitionKey(name = "sub_connect_provider", value = 1)
    @Index(name = INDEX_IDENTIFIER, pkColumn = "sub_connect_provider", pkValue = 2)
    private String subConnectProvider;  // google / facebook / x / email / passkey ...

    @Column(name = "account_detail", type = Column.Type.STRING, isDefined = true)
    private String accountDetail;

    @Column(name = "wallet_address", type = Column.Type.STRING, isDefined = true)
    @Index(name = INDEX_IDENTIFIER, dfColumn = "wallet_address")
    private String walletAddress;

    @Column(name = "status", type = Column.Type.STRING, isDefined = true)
    @Index(name = INDEX_IDENTIFIER, dfColumn = "status")
    private String status;          // pending, active, disabled

    @Column(name = "wallet_type", type = Column.Type.STRING, isDefined = true)
    @Index(name = INDEX_IDENTIFIER, dfColumn = "wallet_type")
    private String walletType;      // EVM / Solana / SOCIAL

    @Column(name = "connect_type", type = Column.Type.STRING, isDefined = true)
    @Index(name = INDEX_IDENTIFIER, dfColumn = "connect_type")
    private String connectType;     // BIND / KEY

    @Column(name = "connected_at", type = Column.Type.INTEGER, isDefined = true)
    @Index(name = INDEX_IDENTIFIER, dfColumn = "connected_at")
    private Date connectedAt;

    @Column(name = "updated_at", type = Column.Type.INTEGER, isDefined = true)
    @Index(name = INDEX_IDENTIFIER, dfColumn = "updated_at")
    private Date updatedAt;

    @Column(name = "disconnected_at", type = Column.Type.INTEGER, isDefined = true)
    @Index(name = INDEX_IDENTIFIER, dfColumn = "disconnected_at")
    private Date disconnectedAt;
}
