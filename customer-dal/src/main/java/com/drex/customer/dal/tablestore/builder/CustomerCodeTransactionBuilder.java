package com.drex.customer.dal.tablestore.builder;

import com.drex.customer.dal.tablestore.model.CustomerCodeTransaction;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.model.TokenPage;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/5/22 11:17 AM
 * @modify
 */
public interface CustomerCodeTransactionBuilder {

    boolean create(CustomerCodeTransaction customerCodeTransaction);


    List<CustomerCodeTransaction> getCodesUser(String[] usedCode);

    CustomerCodeTransaction getCodesUser(String usedCode);


    long getUsedCount(String code);

     long getUsedCount(String customerId, List<String> types, List<String> scenes);

     long getByUser(String id);

     long getBetween(String code, Long start, Long end);

     long getByUseTime(String code, Long start, Long end);

     long getIntervalUserCount(String scene, String customerId, Long start, Long end);

     CustomerCodeTransaction findDefaultInvite(String id);

     List<String> getUsedScenes(String customerId);

     List<CustomerCodeTransaction> getUseList(String userId, String scene);

     List<CustomerCodeTransaction> queryByUserType(String userId, String type);

     CustomerCodeTransaction getByCodeAndUser(String code, String userId);

     Page<CustomerCodeTransaction> pageByOwnerIdType(String ownerId, String type, int offset, int limit);


     TokenPage<CustomerCodeTransaction> inviteList(String code, Integer limit, String nextToken);
}
