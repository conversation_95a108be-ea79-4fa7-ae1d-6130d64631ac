package com.drex.customer.dal.tablestore.builder;

import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.drex.customer.dal.tablestore.model.CustomerCodes;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.model.TokenPage;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/5/22 11:17 AM
 * @modify
 */

public interface CustomerCodesBuilder {

    /**
     * 创造一条code
     */
    boolean create(CustomerCodes customerCodesDO);

    /**
     * 获取code 信息
     */
    CustomerCodes getByCode(String code);

    /**
     * 更新code状态
     */
    boolean updateStatusOff(CustomerCodes customerCodesDO);

    /**
     * 获取默认邀请码
     */
    CustomerCodes getDefaultCode(String customerId);

    /**
     * 批量判断code 是否存在
     */
    List<String> getByBatchCode(List<String> codes);

    /**
     * 批量判断 source是否存在
     */
    List<String> getByBatchSource(List<String> sources);

    /**
     * nextToken 获取code信息
     */
    TokenPage getByCustomerId(String[] types, String customerId, String status, int limit, String nextToken);

    /**
     * 查询用户code list
     */
    Page<CustomerCodes> getByCustomerId(String[] scenes, String[] types, String customerId, String status, int offset, int limit);

    boolean batchPutRow(List<CustomerCodes> ts, RowExistenceExpectation expectation);

    boolean putRow(CustomerCodes t);
}
