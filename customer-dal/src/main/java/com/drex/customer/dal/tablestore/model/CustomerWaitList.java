package com.drex.customer.dal.tablestore.model;

import com.drex.customer.dal.tablestore.constant.Constant;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/12 18:54
 * @description:
 */
@Data
@Table(name = Constant.TABLE_NAME_CUSTOMER_WAIT_LIST)
public class CustomerWaitList implements Serializable {

    @PartitionKey(name = Constant.COLUMN_NAME_EMAIL)
    private String email;

    @Column(name = Constant.COLUMN_NAME_NAME, type = Column.Type.STRING)
    private String name;

    @Column(name = Constant.COLUMN_NAME_PROJECT, type = Column.Type.STRING)
    private String project;

    @Column(name = Constant.COLUMN_NAME_EVM_ADDRESS, type = Column.Type.STRING)
    private String evmAddress;

    @Column(name = Constant.COLUMN_NAME_CREATE_TIME, type = Column.Type.INTEGER)
    private Long createTime;
}
