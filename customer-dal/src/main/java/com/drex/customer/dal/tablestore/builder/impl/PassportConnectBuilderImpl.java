package com.drex.customer.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.drex.customer.dal.tablestore.builder.PassportConnectBuilder;
import com.drex.customer.dal.tablestore.model.PassportConnect;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class PassportConnectBuilderImpl extends WideColumnStoreBuilder<PassportConnect> implements PassportConnectBuilder {

    @Override
    public String tableName() {
        return PassportConnect.TABLE_NAME;
    }

    @PostConstruct
    public void init() {
        super.init(PassportConnect.class);
    }

    @Override
    public PassportConnect getByIdentifier(String walletAddress, String subConnectProvider) {
        if (StringUtils.isBlank(walletAddress) || StringUtils.isBlank(subConnectProvider)) {
            return null;
        }
        walletAddress = walletAddress.toLowerCase();
        List<RangeQueryParameter> parameterList = new ArrayList<>();
        parameterList.add(new RangeQueryParameter("identifier", PrimaryKeyValue.fromString(walletAddress), PrimaryKeyValue.fromString(walletAddress)));
        parameterList.add(new RangeQueryParameter("sub_connect_provider", PrimaryKeyValue.fromString(subConnectProvider), PrimaryKeyValue.fromString(subConnectProvider)));
        parameterList.add(new RangeQueryParameter("passport_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return super.rangeQueryOne(parameterList);
    }

    @Override
    public List<PassportConnect> getByIdentifier(String walletAddress) {
        if (StringUtils.isBlank(walletAddress)) {
            return null;
        }
        walletAddress = walletAddress.toLowerCase();
        List<RangeQueryParameter> parameterList = new ArrayList<>();
        parameterList.add(new RangeQueryParameter("identifier", PrimaryKeyValue.fromString(walletAddress), PrimaryKeyValue.fromString(walletAddress)));
        parameterList.add(new RangeQueryParameter("sub_connect_provider", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        parameterList.add(new RangeQueryParameter("passport_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return super.rangeQuery(parameterList);
    }

    @Override
    public Boolean save(PassportConnect passportConnect) {
        passportConnect.setIdentifier(passportConnect.getIdentifier().toLowerCase());
        return putRow(passportConnect);
    }

    @Override
    public List<PassportConnect> getByPassportId(String passportId) {
        List<RangeQueryParameter> parameterList = new ArrayList<>();
        parameterList.add(new RangeQueryParameter("passport_id", PrimaryKeyValue.fromString(passportId), PrimaryKeyValue.fromString(passportId)));
        parameterList.add(new RangeQueryParameter("identifier", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        parameterList.add(new RangeQueryParameter("sub_connect_provider", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return super.rangeQuery(PassportConnect.INDEX_IDENTIFIER, parameterList);
    }

    @Override
    public List<PassportConnect> listAllPassportConnect() {
        List<RangeQueryParameter> parameterList = new ArrayList<>();
        parameterList.add(new RangeQueryParameter("passport_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        parameterList.add(new RangeQueryParameter("identifier", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        parameterList.add(new RangeQueryParameter("sub_connect_provider", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return super.rangeQuery(PassportConnect.INDEX_IDENTIFIER, parameterList);
    }

    @Override
    public PassportConnect getByPassportIdAndWalletAddress(String passportId, String walletAddress) {
        List<RangeQueryParameter> parameterList = new ArrayList<>();
        parameterList.add(new RangeQueryParameter("passport_id", PrimaryKeyValue.fromString(passportId), PrimaryKeyValue.fromString(passportId)));
        parameterList.add(new RangeQueryParameter("identifier", PrimaryKeyValue.fromString(walletAddress), PrimaryKeyValue.fromString(walletAddress)));
        parameterList.add(new RangeQueryParameter("sub_connect_provider", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return super.rangeQueryOne(PassportConnect.INDEX_IDENTIFIER, parameterList);
    }

    @Override
    public Boolean updatePassportConnect(PassportConnect passportConnect) {
        return super.updateRow(passportConnect);
    }

    @Override
    public List<PassportConnect> getByWalletType(String walletType) {
        if (StringUtils.isBlank(walletType)) {
            return new ArrayList<>();
        }

        // 使用搜索索引查询所有指定walletType的记录
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term("wallet_type", walletType))
                .must(QueryBuilders.term("status", "active")); // 只查询活跃状态的记录

        // 使用分页查询获取所有结果
        List<PassportConnect> allResults = new ArrayList<>();
        String nextToken = null;
        int batchSize = 1000; // 每批查询1000条

        do {
            TokenPage<PassportConnect> page = pageSearchQuery(boolQuery.build(),
                    new Sort(List.of(new FieldSort("passport_id", SortOrder.ASC))),
                    nextToken, batchSize, PassportConnect.INDEX_IDENTIFIER);

            if (page != null && !CollectionUtils.isEmpty(page.getRows())) {
                allResults.addAll(page.getRows());
                nextToken = page.getNextToken();
            } else {
                break;
            }
        } while (StringUtils.isNotBlank(nextToken));

        return allResults;
    }
}
