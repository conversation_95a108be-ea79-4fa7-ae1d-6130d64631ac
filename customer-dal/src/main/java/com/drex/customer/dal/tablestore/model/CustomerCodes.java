package com.drex.customer.dal.tablestore.model;


import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: joseph.xiang
 * @date: 2024/5/15 17:22
 * @desc: cdk兑换表
 */
@Data
@Accessors(chain = true)
@Table(name = "customer_codes")
public class CustomerCodes implements Serializable {

    public static final String TABLE_NAME = "customer_codes";
    public static final String INDEX_CUSTOMER_CODES_DEFAULT_INDEX = "idx_customer_codes_default_index_v2";
    public static final String IDX_CUSTOMER_CODES_BY_SCENE = "idx_customer_codes_by_scene_v2";

    @SearchIndex(name = INDEX_CUSTOMER_CODES_DEFAULT_INDEX, column = "code")
    @Index(name = IDX_CUSTOMER_CODES_BY_SCENE, pkColumn = "code", pkValue = 2)
    @PartitionKey(name = "code")
    private String code;

    @SearchIndex(name = INDEX_CUSTOMER_CODES_DEFAULT_INDEX, column = "customer_id")
    @Index(name = IDX_CUSTOMER_CODES_BY_SCENE, pkColumn = "customer_id", pkValue = 0)
    @Column(name = "customer_id", isDefined = true)
    private String customerId;

    @SearchIndex(name = INDEX_CUSTOMER_CODES_DEFAULT_INDEX, column = "status")
    @Index(name = IDX_CUSTOMER_CODES_BY_SCENE, dfColumn = "status")
    @Column(name = "status", isDefined = true)
    private String status;

    @Index(name = IDX_CUSTOMER_CODES_BY_SCENE, dfColumn = "type")
    @SearchIndex(name = INDEX_CUSTOMER_CODES_DEFAULT_INDEX, column = "type")
    @Column(name = "type", isDefined = true)
    private String type;

    @Index(name = IDX_CUSTOMER_CODES_BY_SCENE, pkColumn = "scene", pkValue = 1)
    @SearchIndex(name = INDEX_CUSTOMER_CODES_DEFAULT_INDEX, column = "scene")
    @Column(name = "scene", isDefined = true)
    private String scene;

    @Index(name = IDX_CUSTOMER_CODES_BY_SCENE, dfColumn = "total_limit_cycle")
    @SearchIndex(name = INDEX_CUSTOMER_CODES_DEFAULT_INDEX, column = "total_limit_cycle")
    @Column(name = "total_limit_cycle", isDefined = true)
    private String totalLimitCycle;

    @Index(name = IDX_CUSTOMER_CODES_BY_SCENE, dfColumn = "total_limit_number")
    @SearchIndex(name = INDEX_CUSTOMER_CODES_DEFAULT_INDEX, column = "total_limit_number", fieldType = FieldType.LONG)
    @Column(name = "total_limit_number", isDefined = true)
    private Integer totalLimitNumber;

    @Index(name = IDX_CUSTOMER_CODES_BY_SCENE, dfColumn = "total_limit_used")
    @SearchIndex(name = INDEX_CUSTOMER_CODES_DEFAULT_INDEX, column = "total_limit_used", fieldType = FieldType.LONG)
    @Column(name = "total_limit_used", isDefined = true)
    private Integer totalLimitUsed;

    @Index(name = IDX_CUSTOMER_CODES_BY_SCENE, dfColumn = "user_limit_cycle")
    @SearchIndex(name = INDEX_CUSTOMER_CODES_DEFAULT_INDEX, column = "user_limit_cycle")
    @Column(name = "user_limit_cycle", isDefined = true)
    private String userLimitCycle;

    @Index(name = IDX_CUSTOMER_CODES_BY_SCENE, dfColumn = "user_limit_number")
    @SearchIndex(name = INDEX_CUSTOMER_CODES_DEFAULT_INDEX, column = "user_limit_number", fieldType = FieldType.LONG)
    @Column(name = "user_limit_number", isDefined = true)
    private Integer userLimitNumber;

    @SearchIndex(name = INDEX_CUSTOMER_CODES_DEFAULT_INDEX, column = "source")
    @Column(name = "source", isDefined = true)
    private String source;

    @SearchIndex(name = INDEX_CUSTOMER_CODES_DEFAULT_INDEX, column = "create_time", fieldType = FieldType.LONG)
    @Column(name = "create_time", isDefined = true)
    private Date createTime;

    @SearchIndex(name = INDEX_CUSTOMER_CODES_DEFAULT_INDEX, column = "modify_used_time", fieldType = FieldType.LONG)
    @Column(name = "modify_used_time", isDefined = true)
    private Date modifyUsedTime;

    @SearchIndex(name = INDEX_CUSTOMER_CODES_DEFAULT_INDEX, column = "sort", fieldType = FieldType.LONG)
    @Column(name = "sort", isDefined = true)
    private Integer sort;

    @SearchIndex(name = INDEX_CUSTOMER_CODES_DEFAULT_INDEX, column = "expiration_time", fieldType = FieldType.LONG)
    @Column(name = "expiration_time", isDefined = true)
    private Date expirationTime;

    @SearchIndex(name = INDEX_CUSTOMER_CODES_DEFAULT_INDEX, column = "can_reuse", fieldType = FieldType.BOOLEAN)
    @Column(name = "can_reuse", isDefined = true)
    private Boolean canReuse=false;

    /**
     * 遗留字段，暂时不用
     * */
    @Index(name = IDX_CUSTOMER_CODES_BY_SCENE, dfColumn = "level")
    @SearchIndex(name = INDEX_CUSTOMER_CODES_DEFAULT_INDEX, column = "level", fieldType = FieldType.LONG)
    @Column(name = "level", isDefined = true)
    private Integer level;

    /**
     * 使用总次数
     * 新服务、码可以使用改字段，如果是旧服务同步过数据的，数据不统一不可使用
     */
    @Index(name = IDX_CUSTOMER_CODES_BY_SCENE, dfColumn = "use_count")
    @SearchIndex(name = INDEX_CUSTOMER_CODES_DEFAULT_INDEX, column = "use_count", fieldType = FieldType.LONG)
    @Column(name = "use_count", isDefined = true)
    private Integer useCount;

}
