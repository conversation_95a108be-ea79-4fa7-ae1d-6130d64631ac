package com.drex.customer.dal.tablestore.builder;

import com.drex.customer.dal.tablestore.model.CustomerReferral;

public interface CustomerReferralBuilder {

    CustomerReferral getByCustomerId(String customerId, String businessType);

    long countByReferrerId(String referrerId);

    Boolean insert(CustomerReferral CustomerReferral);

    boolean updateReferrerStatus(String customerId, String status, String businessType);
}
