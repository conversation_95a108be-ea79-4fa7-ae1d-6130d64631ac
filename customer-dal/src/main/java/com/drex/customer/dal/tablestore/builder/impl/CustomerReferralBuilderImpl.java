package com.drex.customer.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.ColumnValue;
import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.condition.SingleColumnValueCondition;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.agg.AggregationBuilders;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.drex.customer.api.constants.CustomerConstants;
import com.drex.customer.dal.tablestore.builder.CustomerReferralBuilder;
import com.drex.customer.dal.tablestore.constant.Constant;
import com.drex.customer.dal.tablestore.model.CustomerReferral;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Repository;

@Repository
public class CustomerReferralBuilderImpl extends WideColumnStoreBuilder<CustomerReferral> implements CustomerReferralBuilder {

    @PostConstruct
    public void init(){
        super.init(CustomerReferral.class);
    }

    public CustomerReferral getByCustomerId(String customerId, String businessType) {
        CustomerReferral customerReferral = new CustomerReferral();
        customerReferral.setCustomerId(customerId);
        customerReferral.setBusinessType(businessType);
        return super.getRow(customerReferral);
    }

    @Override
    public long countByReferrerId(String referrerId) {
        String aggName = "count_agg_customer_referral_by_referrer_id";
        BoolQuery.Builder queryBuilder = QueryBuilders.bool();
        queryBuilder.must(QueryBuilders.term(Constant.COLUMN_NAME_REFERRER_ID, referrerId));
        queryBuilder.must(QueryBuilders.term(Constant.COLUMN_NAME_STATUS, CustomerConstants.RefferalStatus.VALID.getKey()));

        SearchQuery query = SearchQuery.newBuilder()
                .query(queryBuilder)
                .addAggregation(AggregationBuilders.count(aggName, Constant.COLUMN_NAME_CUSTOMER_ID)).build();

        return searchCount(query, Constant.INDEX_NAME_CUSTOMER_REFERRAL_INDEX, aggName);
    }

    @Override
    public Boolean insert(CustomerReferral CustomerReferral) {
        return putRow(CustomerReferral);
    }

    @Override
    public boolean updateReferrerStatus(String customerId, String status, String businessType) {
        CustomerReferral customerReferral = new CustomerReferral();
        customerReferral.setCustomerId(customerId);
        customerReferral.setBusinessType("bindInviteCode");
        customerReferral.setStatus(status);
        Condition condition = new Condition(RowExistenceExpectation.EXPECT_EXIST);
        condition.setColumnCondition(new SingleColumnValueCondition("status", SingleColumnValueCondition.CompareOperator.NOT_EQUAL, ColumnValue.fromString(status)));
        return super.updateRow(customerReferral, condition);
    }

    @Override
    public String tableName() {
        return Constant.TABLE_NAME_CUSTOMER_REFERRAL;
    }
}
