package com.drex.customer.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.drex.customer.dal.tablestore.IdUtils;
import com.drex.customer.dal.tablestore.builder.CustomerBuilder;
import com.drex.customer.dal.tablestore.constant.Constant;
import com.drex.customer.dal.tablestore.model.Customer;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Repository("customerBuilder")
public class CustomerBuilderImpl extends WideColumnStoreBuilder<Customer> implements CustomerBuilder {

    @Autowired
    private IdUtils idUtils;

    public String getTableName(){
        return Constant.TABLE_NAME_CUSTOMER_REFERRAL;
    }

    @PostConstruct
    public void init() {
        super.init(Customer.class);
    }

    @Override
    public Customer getByRegisterAddress(String registerAddress) {

        BoolQuery.Builder boolQueryBuilder = QueryBuilders.bool()
                .should(QueryBuilders.term(Constant.COLUMN_NAME_REGISTER_ADDRESS, registerAddress))
                .should(QueryBuilders.term(Constant.COLUMN_NAME_CONNECT_ADDRESS, registerAddress))
                .should(QueryBuilders.term(Constant.COLUMN_NAME_LOWER_CONNECT_ADDRESS, registerAddress));

        return super.searchOne(boolQueryBuilder.build(), Constant.INDEX_NAME_CUSTOMER_EOA_INDEX);
    }

    @Override
    public Customer getById(String customerId) {
        Customer customer = new Customer();
        customer.setCustomerId(customerId);
        return getRow(customer);
    }

    @Override
    public Customer getByInviteCode(String inviteCode) {
        List<RangeQueryParameter> queryList = new ArrayList<>();
        queryList.add(new RangeQueryParameter(Constant.COLUMN_NAME_INVITE_CODE, PrimaryKeyValue.fromString(inviteCode), PrimaryKeyValue.fromString(inviteCode)));
        queryList.add(new RangeQueryParameter(Constant.COLUMN_NAME_CUSTOMER_ID, PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return super.rangeQueryOne(Constant.INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX, queryList);
    }

    @Override
    public Boolean insert(Customer customer) {
        if(customer.getCustomerId() == null){
            customer.setCustomerId(idUtils.nextId());
        }
        customer.setStatus(Constant.CommonStatus.valid.getStatus());
        customer.setRegisterTime(System.currentTimeMillis());
        return super.putRow(customer);
    }

    @Override
    public Boolean updateLevel(String customerId, String kycLevel) {
        Customer customer = new Customer();
        customer.setCustomerId(customerId);
        customer.setKycLevel(kycLevel);
        return super.updateRow(customer, Collections.singletonList(Constant.COLUMN_NAME_KYC_LEVEL));
    }

    @Override
    public Boolean update(Customer customer) {
        return super.updateRow(customer);
    }
}
