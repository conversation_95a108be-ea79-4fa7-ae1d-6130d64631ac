// Passport.java
package com.drex.customer.dal.tablestore.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;
import java.util.Date;

@Data
@Table(name = "passport")
public class Passport implements java.io.Serializable{

    public static final String TABLE_NAME = "passport";
    public static final String SEARCH_INDEX_PASSPORT = "search_index_passport";

    // Primary Key
    @PartitionKey(name = "passport_id", type = PartitionKey.Type.STRING)
    @SearchIndex(name = SEARCH_INDEX_PASSPORT, column = "passport_id")
    private String passportId;       // kseq generated

    @SearchIndex(name = SEARCH_INDEX_PASSPORT, column = "passport_chain_id")
    @Column(name = "passport_chain_id", type = Column.Type.STRING)
    private String passportChainId;

    @SearchIndex(name = SEARCH_INDEX_PASSPORT, column = "contract_address")
    @Column(name = "contract_address", type = Column.Type.STRING)
    private String contractAddress;

    @Column(name = "number", type = Column.Type.INTEGER)
    @SearchIndex(name = SEARCH_INDEX_PASSPORT, column = "number")
    private Long number;           // On-chain ID

    @Column(name = "user_id", type = Column.Type.STRING)
    @SearchIndex(name = SEARCH_INDEX_PASSPORT, column = "user_id")
    private String userId;

    @Column(name = "handle_name", type = Column.Type.STRING)
    @SearchIndex(name = SEARCH_INDEX_PASSPORT, column = "handle_name")
    private String handleName;

    @Column(name = "lower_handle_name", type = Column.Type.STRING)
    @SearchIndex(name = SEARCH_INDEX_PASSPORT, column = "lower_handle_name")
    private String lowerHandleName;

    @Column(name = "name", type = Column.Type.STRING)
    @SearchIndex(name = SEARCH_INDEX_PASSPORT, column = "name")
    private String username;

    @Column(name = "avatar", type = Column.Type.STRING)
    @SearchIndex(name = SEARCH_INDEX_PASSPORT, column = "avatar")
    private String avatar;

    @Column(name = "email", type = Column.Type.STRING)
    @SearchIndex(name = SEARCH_INDEX_PASSPORT, column = "email")
    private String email;

    @Column(name = "phone", type = Column.Type.STRING)
    @SearchIndex(name = SEARCH_INDEX_PASSPORT, column = "phone")
    private String phone;

    @Column(name = "referral_code", type = Column.Type.STRING)
    @SearchIndex(name = SEARCH_INDEX_PASSPORT, column = "referral_code")
    private String referralCode;

    @Column(name = "create_time", type = Column.Type.INTEGER)
    @SearchIndex(name = SEARCH_INDEX_PASSPORT, column = "create_time")
    private Date createdAt;

    @Column(name = "update_time", type = Column.Type.INTEGER)
    @SearchIndex(name = SEARCH_INDEX_PASSPORT, column = "update_time")
    private Date updatedAt;

    @Column(name = "status", type = Column.Type.STRING)
    @SearchIndex(name = SEARCH_INDEX_PASSPORT, column = "status")
    private String status;// pending, active
}
