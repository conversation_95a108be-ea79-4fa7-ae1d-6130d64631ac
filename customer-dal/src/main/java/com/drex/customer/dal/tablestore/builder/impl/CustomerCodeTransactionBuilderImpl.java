package com.drex.customer.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.SyncClient;
import com.alicloud.openservices.tablestore.model.ColumnValue;
import com.alicloud.openservices.tablestore.model.Direction;
import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.SearchRequest;
import com.alicloud.openservices.tablestore.model.search.SearchResponse;
import com.alicloud.openservices.tablestore.model.search.agg.AggregationBuilders;
import com.alicloud.openservices.tablestore.model.search.groupby.GroupByBuilders;
import com.alicloud.openservices.tablestore.model.search.groupby.GroupByFieldResultItem;
import com.alicloud.openservices.tablestore.model.search.query.*;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.drex.customer.api.constants.CustomerConstants;
import com.drex.customer.dal.tablestore.builder.CustomerCodeTransactionBuilder;
import com.drex.customer.dal.tablestore.model.CustomerCodeTransaction;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.framework.ots.RangeResult;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import static com.drex.customer.dal.tablestore.model.CustomerCodeTransaction.IDX_CUSTOMER_CODE_TRAN_BY_SCENE;

@Slf4j
@Repository
public class CustomerCodeTransactionBuilderImpl extends WideColumnStoreBuilder<CustomerCodeTransaction> implements CustomerCodeTransactionBuilder {

    @Resource
    private SyncClient syncClient;
    @PostConstruct
    public void init() {
        init(CustomerCodeTransaction.class);
    }
    public boolean create(CustomerCodeTransaction customerCodeTransaction) {
        return putRow(customerCodeTransaction);
    }


    public List<CustomerCodeTransaction> getCodesUser(String[] usedCode) {
        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.filter(QueryBuilders.terms("code").terms(usedCode));
        Sort sort = new Sort(List.of(new FieldSort("use_time", SortOrder.DESC)));
        List<CustomerCodeTransaction> list = search(builder.build(), sort, 0, usedCode.length, CustomerCodeTransaction.INDEX_CUSTOMER_CODES_TRAN_DEFAULT_INDEX);
        return list;
    }

    public CustomerCodeTransaction getCodesUser(String usedCode) {
        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.filter(QueryBuilders.terms("code").terms(usedCode));
        Sort sort = new Sort(List.of(new FieldSort("use_time", SortOrder.DESC)));
        CustomerCodeTransaction list = searchOne(builder.build(), CustomerCodeTransaction.INDEX_CUSTOMER_CODES_TRAN_DEFAULT_INDEX);
        return list;
    }


    public long getUsedCount(String code) {
        SearchQuery.Builder builder = SearchQuery.newBuilder()
                .query(QueryBuilders.term("code", code))
                .limit(0)
                .addAggregation(AggregationBuilders.count("user_id", "user_id"));
        return searchCount(builder.build(), CustomerCodeTransaction.INDEX_CUSTOMER_CODES_TRAN_DEFAULT_INDEX, "user_id");
    }

    public long getUsedCount(String customerId, List<String> types, List<String> scenes) {
        BoolQuery boolQuery = new BoolQuery();
        List<Query> queryList = new ArrayList<>();

        BoolQuery shouldQuery = new BoolQuery();
        List<Query> shouldQueryList = new ArrayList<>();

        //拥有者
        TermQuery termQuery = new TermQuery();
        termQuery.setFieldName("owner_id");
        termQuery.setTerm(ColumnValue.fromString(customerId));
        queryList.add(termQuery);
        // 多个类型or
        if (!types.isEmpty()) {
            TermsQuery termsQuery = new TermsQuery();
            termsQuery.setFieldName("type");
            for (String type : types) {
                termsQuery.addTerm(ColumnValue.fromString(type));
            }
            shouldQueryList.add(termsQuery);
        }
        // 多个场景or
        if (!scenes.isEmpty()) {
            TermsQuery termsQuery = new TermsQuery();
            termsQuery.setFieldName("scene");
            for (String scene : scenes) {
                termsQuery.addTerm(ColumnValue.fromString(scene));
            }
            shouldQueryList.add(termsQuery);
        }

        SearchQuery.Builder builder = SearchQuery.newBuilder()
                .limit(0)
                .addAggregation(AggregationBuilders.count("user_id", "user_id"));
        SearchQuery searchQuery = builder.build();
        shouldQuery.setShouldQueries(shouldQueryList);


        queryList.add(shouldQuery);
        boolQuery.setMustQueries(queryList);
        searchQuery.setQuery(boolQuery);

        return searchCount(searchQuery, CustomerCodeTransaction.INDEX_CUSTOMER_CODES_TRAN_DEFAULT_INDEX, "user_id");
    }

    public long getByUser(String id) {
        SearchQuery.Builder builder = SearchQuery.newBuilder()
                .query(QueryBuilders.term("user_id", id))
                .limit(0)
                .addAggregation(AggregationBuilders.count("code", "code"));
        return searchCount(builder.build(), CustomerCodeTransaction.INDEX_CUSTOMER_CODES_TRAN_DEFAULT_INDEX, "code");

    }

    public long getBetween(String code, Long start, Long end) {
        BoolQuery.Builder queryBuilder = QueryBuilders.bool();
        queryBuilder.must(QueryBuilders.range("use_time").greaterThan(start).lessThanOrEqual(end));
        queryBuilder.must(QueryBuilders.term("code", code));

        SearchQuery.Builder builder = SearchQuery.newBuilder()
                .query(queryBuilder)
                .limit(0)
                .addAggregation(AggregationBuilders.count("code", "code"));
        return searchCount(builder.build(), CustomerCodeTransaction.INDEX_CUSTOMER_CODES_TRAN_DEFAULT_INDEX, "code");
    }

    public long getByUseTime(String code, Long start, Long end) {
        RangeQueryParameter codeParameter = new RangeQueryParameter("code", PrimaryKeyValue.fromString(code), PrimaryKeyValue.fromString(code));
        RangeQueryParameter useTimeParameter = new RangeQueryParameter("use_time", PrimaryKeyValue.fromLong(start), PrimaryKeyValue.fromLong(end));
        RangeQueryParameter useIdParameter = new RangeQueryParameter("user_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX);
        long total = 0;
        PrimaryKey token = null;
        do {
            RangeResult<CustomerCodeTransaction> result = this.rangeQueryWithNextToken(mapping.getTableName(), List.of(codeParameter, useTimeParameter,useIdParameter), null, Direction.FORWARD, 500, token);
            total += result.list.size();
            token = result.nextToken;
        } while (token != null);
        return total;
    }

    public long getIntervalUserCount(String scene, String customerId, Long start, Long end) {
        RangeQueryParameter sceneParameter = new RangeQueryParameter("scene", PrimaryKeyValue.fromString(scene), PrimaryKeyValue.fromString(scene));
        RangeQueryParameter useIdParameter = new RangeQueryParameter("user_id", PrimaryKeyValue.fromString(customerId), PrimaryKeyValue.fromString(customerId));
        RangeQueryParameter useTimeParameter = new RangeQueryParameter("use_time", PrimaryKeyValue.fromLong(start), PrimaryKeyValue.fromLong(end));
        RangeQueryParameter codeParameter = new RangeQueryParameter("code", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX);
        long total = 0;
        PrimaryKey token = null;
        do {
            RangeResult<CustomerCodeTransaction> result = this.rangeQueryWithNextToken(IDX_CUSTOMER_CODE_TRAN_BY_SCENE, List.of(sceneParameter, useIdParameter, useTimeParameter, codeParameter), null, Direction.FORWARD, 500, token);
            total += result.list.size();
            token = result.nextToken;
        } while (token != null);
        return total;
    }

    public CustomerCodeTransaction findDefaultInvite(String id) {
        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.must(QueryBuilders.terms("user_id").terms(id));
        builder.must(QueryBuilders.terms("type").terms(CustomerConstants.CodeType.DEFAULT_INVITE.getName()));
        Sort sort = new Sort(List.of(new FieldSort("use_time", SortOrder.DESC)));
        CustomerCodeTransaction list = searchOne(builder.build(), CustomerCodeTransaction.INDEX_CUSTOMER_CODES_TRAN_DEFAULT_INDEX);
        return list;
    }

    public List<String> getUsedScenes(String customerId) {
        log.info("getUsedScenes customerId:{}", customerId);
        List<String> result = new ArrayList<>();
        SearchRequest searchRequest = SearchRequest.newBuilder()
                .tableName(CustomerCodeTransaction.TABLE_NAME)
                .indexName(CustomerCodeTransaction.INDEX_CUSTOMER_CODES_TRAN_DEFAULT_INDEX)
                .searchQuery(
                        SearchQuery.newBuilder()
                                .query(QueryBuilders.bool().must(QueryBuilders.term("user_id", customerId)))
                                .limit(0) //如果只关心统计聚合结果，不关心具体数据，您可以将limit设置为0来提高性能。
                                .addGroupBy(GroupByBuilders
                                        .groupByField("scene", "scene").size(2000)
                                )
                                .build())
                .build();
        //执行查询。
        SearchResponse resp = syncClient.search(searchRequest);
        for (GroupByFieldResultItem item : resp.getGroupByResults().getAsGroupByFieldResult("scene").getGroupByFieldResultItems()) {
            result.add(item.getKey());
        }
        // DEFAULT_INVITE 表中没有记录scene，单独查询下
        BoolQuery query = QueryBuilders.bool()
                .must(QueryBuilders.term("user_id", customerId))
                .must(QueryBuilders.term("type", CustomerConstants.CodeType.DEFAULT_INVITE))
                .build();
        List<CustomerCodeTransaction> defaultList = search(query, null, 0, 10, CustomerCodeTransaction.INDEX_CUSTOMER_CODES_TRAN_DEFAULT_INDEX);
        if (!defaultList.isEmpty()) {
            result.add("DEFAULT_INVITE");
        }

        return result;
    }

    public List<CustomerCodeTransaction> getUseList(String userId, String scene) {
        List<RangeQueryParameter> queryList = new ArrayList<>();
        queryList.add(new RangeQueryParameter("scene", PrimaryKeyValue.fromString(scene), PrimaryKeyValue.fromString(scene)));
        queryList.add(new RangeQueryParameter("user_id", PrimaryKeyValue.fromString(userId), PrimaryKeyValue.fromString(userId)));
        queryList.add(new RangeQueryParameter("use_time", PrimaryKeyValue.INF_MAX, PrimaryKeyValue.INF_MIN));
        queryList.add(new RangeQueryParameter("code", PrimaryKeyValue.INF_MAX, PrimaryKeyValue.INF_MIN));
        List<CustomerCodeTransaction> list = rangeQuery(IDX_CUSTOMER_CODE_TRAN_BY_SCENE, queryList, Direction.BACKWARD);
        return list;
    }

    public List<CustomerCodeTransaction> queryByUserType(String userId, String type) {
        BoolQuery query = QueryBuilders.bool()
                .must(QueryBuilders.term("user_id", userId))
                .must(QueryBuilders.term("type", type))
                .build();
        Sort sort = new Sort(List.of(new FieldSort("use_time", SortOrder.DESC)));
        return search(query, sort, 0, 10, CustomerCodeTransaction.INDEX_CUSTOMER_CODES_TRAN_DEFAULT_INDEX);
    }

    public CustomerCodeTransaction getByCodeAndUser(String code, String userId) {
        List<RangeQueryParameter> queryParameterList = new ArrayList<>();
        queryParameterList.add(new RangeQueryParameter("code", PrimaryKeyValue.fromString(code), PrimaryKeyValue.fromString(code)));
        queryParameterList.add(new RangeQueryParameter("user_id", PrimaryKeyValue.fromString(userId), PrimaryKeyValue.fromString(userId)));
        queryParameterList.add(new RangeQueryParameter("use_time", PrimaryKeyValue.INF_MAX, PrimaryKeyValue.INF_MIN));
        return rangeQueryOne(CustomerCodeTransaction.IDX_CUSTOMER_CODE_TRAN_BY_CODE_USER, queryParameterList, Direction.BACKWARD);
    }


    public Page<CustomerCodeTransaction> pageByOwnerIdType(String ownerId, String type, int offset, int limit) {
        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.filter(QueryBuilders.terms("owner_id").terms(ownerId));
        builder.filter(QueryBuilders.terms("type").terms(type));
        Sort sort = new Sort(List.of(new FieldSort("use_time", SortOrder.DESC)));
        return pageSearchQuery(builder.build(), sort, offset, limit, CustomerCodeTransaction.INDEX_CUSTOMER_CODES_TRAN_DEFAULT_INDEX);
    }


    public TokenPage<CustomerCodeTransaction> inviteList(String code, Integer limit, String nextToken) {
        BoolQuery.Builder builder = QueryBuilders.bool()
                .must(QueryBuilders.term("code", code));
        Sort sort = new Sort(List.of(new FieldSort("use_time", SortOrder.DESC)));
        return pageSearchQuery(builder.build(), sort, nextToken, limit, CustomerCodeTransaction.INDEX_CUSTOMER_CODES_TRAN_DEFAULT_INDEX);
    }
}
