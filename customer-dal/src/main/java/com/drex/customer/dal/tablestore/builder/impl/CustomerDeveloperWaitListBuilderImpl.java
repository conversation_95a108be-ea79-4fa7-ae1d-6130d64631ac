package com.drex.customer.dal.tablestore.builder.impl;

import cn.hutool.core.util.IdUtil;
import com.drex.customer.dal.tablestore.builder.CustomerDeveloperWaitListBuilder;
import com.drex.customer.dal.tablestore.constant.Constant;
import com.drex.customer.dal.tablestore.model.CustomerDeveloperWaitList;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Repository;

@Repository
public class CustomerDeveloperWaitListBuilderImpl extends WideColumnStoreBuilder<CustomerDeveloperWaitList> implements CustomerDeveloperWaitListBuilder {

    @Override
    public String getTableName() {
        return Constant.TABLE_NAME_CUSTOMER_WAIT_DEVELOPER_LIST;
    }

    @PostConstruct
    public void init() {
        super.init(CustomerDeveloperWaitList.class);
    }

    @Override
    public boolean insertOrUpdate(CustomerDeveloperWaitList customerDeveloperWaitList) {
        customerDeveloperWaitList.setId(IdUtil.objectId());
        return super.putRow(customerDeveloperWaitList);
    }
}
