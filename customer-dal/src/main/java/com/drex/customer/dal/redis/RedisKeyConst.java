package com.drex.customer.dal.redis;

/**
 * <AUTHOR>
 * @date 2025/4/23 19:31
 * @description: Redis Key
 */
public enum RedisKeyConst {
    SOCIAL_AUTH_TOKEN("SOCIAL:AUTH:TOKEN:"),
    ;

    private String prefix;

    RedisKeyConst(String prefix){
        this.prefix = prefix;
    }

    public String getKey(String content){
        return String.format("%s%s", this.prefix, content);
    }

    public static String getKey(String prefix, String content){
        return String.format("%s%s", prefix, content);
    }

    public String getMiddleKey(String content){
        return String.format(this.prefix, content);
    }
}
