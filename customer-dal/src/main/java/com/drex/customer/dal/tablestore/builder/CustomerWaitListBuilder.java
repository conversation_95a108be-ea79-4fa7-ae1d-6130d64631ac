package com.drex.customer.dal.tablestore.builder;

import com.drex.customer.dal.tablestore.model.CustomerWaitList;

/**
 * <AUTHOR>
 * @date 2025/5/12 18:50
 * @description:
 */
public interface CustomerWaitListBuilder {

    String getTableName();

    boolean insertOrUpdate(CustomerWaitList customerWaitList);

    CustomerWaitList getWaitListByAddressOrEmail(String email, String address, long startTime, long endTime);
}
