package com.drex.customer.dal.tablestore.model;

import com.drex.customer.dal.tablestore.constant.Constant;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Table(name = Constant.TABLE_NAME_CUSTOMER_WAIT_DEVELOPER_LIST)
public class CustomerDeveloperWaitList implements Serializable {

    @PartitionKey(name = Constant.COLUMN_NAME_ID)
    private String id;

    @Column(name = Constant.COLUMN_NAME_CONTACT_EMAIL, type = Column.Type.STRING)
    private String contactEmail;

    @Column(name = Constant.COLUMN_NAME_PROJECT_NAME, type = Column.Type.STRING)
    private String projectName;

    @Column(name = Constant.COLUMN_NAME_PROJECT_CATEGORY, type = Column.Type.STRING)
    private String projectCategory;

    @Column(name = Constant.COLUMN_NAME_PROJECT_CATEGORY_OTHER, type = Column.Type.STRING)
    private String projectCategoryOther;

    @Column(name = Constant.COLUMN_NAME_PROJECT_DESCRIPTION, type = Column.Type.STRING)
    private String projectDescription;

    @Column(name = Constant.COLUMN_NAME_ECOSYSTEM_ALIGNMENT, type = Column.Type.STRING)
    private String ecosystemAlignment;

    @Column(name = Constant.COLUMN_NAME_ALIGNMENT_WITH_TREX, type = Column.Type.STRING)
    private String alignmentWithTrex;

    @Column(name = Constant.COLUMN_NAME_PROJECT_STAGE, type = Column.Type.STRING)
    private String projectStage;

    @Column(name = Constant.COLUMN_NAME_PROJECT_WEBSITE, type = Column.Type.STRING)
    private String projectWebsite;

    @Column(name = Constant.COLUMN_NAME_PROJECT_TWITTER, type = Column.Type.STRING)
    private String projectTwitter;

    @Column(name = Constant.COLUMN_NAME_PROJECT_COMMUNITY, type = Column.Type.STRING)
    private String projectCommunity;

    @Column(name = Constant.COLUMN_NAME_PRIMARY_CONTACT_NAME, type = Column.Type.STRING)
    private String primaryContactName;

    @Column(name = Constant.COLUMN_NAME_CONTACT_TELEGRAM_USERNAME, type = Column.Type.STRING)
    private String contactTelegram;

    @Column(name = Constant.COLUMN_NAME_SUPPORT_TYPE, type = Column.Type.STRING)
    private String supportType;

    @Column(name = Constant.COLUMN_NAME_SUPPORT_TYPE_OTHER, type = Column.Type.STRING)
    private String supportTypeOther;

    @Column(name = Constant.COLUMN_NAME_USE_OF_FUNDS, type = Column.Type.STRING)
    private String useOfFunds;

    @Column(name = Constant.COLUMN_NAME_TEAM_BACKGROUND, type = Column.Type.STRING)
    private String teamBackground;

    @Column(name = Constant.COLUMN_NAME_SUPPORTING_DOCUMENTS, type = Column.Type.STRING)
    private String supportingDocuments;

    @Column(name = Constant.COLUMN_NAME_ADDITIONAL_COMMENTS, type = Column.Type.STRING)
    private String additionalComments;

    @Column(name = Constant.COLUMN_NAME_CREATE_TIME, type = Column.Type.INTEGER)
    private Long createTime;
}
