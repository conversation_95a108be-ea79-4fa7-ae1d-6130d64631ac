package com.drex.customer.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.Index;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import com.drex.customer.dal.tablestore.constant.Constant;
import lombok.Data;

import java.io.Serializable;

@Table(name = Constant.TABLE_NAME_CUSTOMER)
@Data
public class Customer implements Serializable {

    @PartitionKey(name = Constant.COLUMN_NAME_CUSTOMER_ID, type = PartitionKey.Type.STRING)
    @Index(name = Constant.INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX, pkColumn = Constant.COLUMN_NAME_CUSTOMER_ID, pkValue = 1)
    private String customerId;

    @Column(name = Constant.COLUMN_NAME_REGISTER_ADDRESS, isDefined = true)
    @SearchIndex(name = Constant.INDEX_NAME_CUSTOMER_EOA_INDEX, column = Constant.COLUMN_NAME_REGISTER_ADDRESS)
    @Index(name = Constant.INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX, dfColumn = Constant.COLUMN_NAME_REGISTER_ADDRESS)
    private String registerAddress;

    @Column(name = Constant.COLUMN_NAME_CONNECT_ADDRESS, isDefined = true)
    @SearchIndex(name = Constant.INDEX_NAME_CUSTOMER_EOA_INDEX, column = Constant.COLUMN_NAME_CONNECT_ADDRESS)
    @Index(name = Constant.INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX, dfColumn = Constant.COLUMN_NAME_CONNECT_ADDRESS)
    private String connectAddress;

    @Column(name = Constant.COLUMN_NAME_LOWER_CONNECT_ADDRESS, isDefined = true)
    @SearchIndex(name = Constant.INDEX_NAME_CUSTOMER_EOA_INDEX, column = Constant.COLUMN_NAME_LOWER_CONNECT_ADDRESS)
    @Index(name = Constant.INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX, dfColumn = Constant.COLUMN_NAME_LOWER_CONNECT_ADDRESS)
    private String lowerConnectAddress;

    @Column(name = Constant.COLUMN_NAME_EOA_ADDRESS, isDefined = true)
    @Index(name = Constant.INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX, dfColumn = Constant.COLUMN_NAME_EOA_ADDRESS)
    private String eoaAddress;

    @Column(name = Constant.COLUMN_NAME_THIRD_USER_ID, isDefined = true)
    @Index(name = Constant.INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX, dfColumn = Constant.COLUMN_NAME_THIRD_USER_ID)
    private String thirdUserId;

    @Column(name = Constant.COLUMN_NAME_USER_PICTURE, isDefined = true)
    @Index(name = Constant.INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX, dfColumn = Constant.COLUMN_NAME_USER_PICTURE)
    private String userPicture;

    @Column(name = Constant.COLUMN_NAME_USER_ID, isDefined = true)
    @Index(name = Constant.INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX, dfColumn = Constant.COLUMN_NAME_USER_ID)
    private String userId;

    @Column(name = Constant.COLUMN_NAME_USERNAME, isDefined = true)
    @Index(name = Constant.INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX, dfColumn = Constant.COLUMN_NAME_USERNAME)
    private String username;

    @Column(name = Constant.COLUMN_NAME_EMAIL, isDefined = true)
    @Index(name = Constant.INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX, dfColumn = Constant.COLUMN_NAME_EMAIL)
    private String email;

    @Column(name = Constant.COLUMN_NAME_CHAIN_ID, isDefined = true, type = Column.Type.INTEGER)
    @Index(name = Constant.INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX, dfColumn = Constant.COLUMN_NAME_CHAIN_ID)
    private Integer chainId;

    @Column(name = Constant.COLUMN_NAME_SMART_ACCOUNT_ADDRESS, isDefined = true)
    @Index(name = Constant.INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX, dfColumn = Constant.COLUMN_NAME_SMART_ACCOUNT_ADDRESS)
    private String smartAccountAddress;

    @Column(name = Constant.COLUMN_NAME_AUTH_PROVIDER, isDefined = true)
    @Index(name = Constant.INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX, dfColumn = Constant.COLUMN_NAME_AUTH_PROVIDER)
    private String authProvider;

    @Column(name = Constant.COLUMN_NAME_KYC_LEVEL, isDefined = true)
    @Index(name = Constant.INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX, dfColumn = Constant.COLUMN_NAME_KYC_LEVEL)
    private String kycLevel = "L0";

    @Column(name = Constant.COLUMN_NAME_REGISTER_TIME, isDefined = true, type = Column.Type.INTEGER)
    @Index(name = Constant.INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX, dfColumn = Constant.COLUMN_NAME_REGISTER_TIME)
    private Long registerTime;

    @Column(name = Constant.COLUMN_NAME_INVITE_CODE, isDefined = true)
    @Index(name = Constant.INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX, pkColumn = Constant.COLUMN_NAME_INVITE_CODE)
    private String inviteCode;

    @Column(name = Constant.COLUMN_NAME_STATUS, isDefined = true, type = Column.Type.INTEGER)
    @Index(name = Constant.INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX, dfColumn = Constant.COLUMN_NAME_STATUS)
    private Integer status;

    @Column(name = Constant.COLUMN_NAME_CLIENT_IP, isDefined = true)
    @Index(name = Constant.INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX, dfColumn = Constant.COLUMN_NAME_CLIENT_IP)
    private String clientIp;

    @Column(name = Constant.COLUMN_NAME_DEVICE_INFO, isDefined = true)
    @Index(name = Constant.INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX, dfColumn = Constant.COLUMN_NAME_DEVICE_INFO)
    private String deviceInfo;

    @Column(name = Constant.COLUMN_NAME_CREATED, type = Column.Type.INTEGER)
    private Long created;
}
