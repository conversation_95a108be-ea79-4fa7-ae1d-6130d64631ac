package com.drex.customer.dal.tablestore.builder;

import com.drex.customer.dal.tablestore.model.Customer;

public interface CustomerBuilder {

    /**
     * 根据EOA地址获取客户信息。
     *
     * @param registerAddress 客户的EOA地址
     * @return 返回与给定EOA地址对应的客户对象
     * //t 如果找不到对应的客户，则返回null
     */
    Customer getByRegisterAddress(String registerAddress);

    /**
     * 根据客户ID获取客户信息。
     *
     * @param customerId 客户的唯一标识符。
     * @return 返回与指定ID对应的客户对象。如果找不到对应的客户，则返回null。
     */
    Customer getById(String customerId);

    /**
     * 根据邀请码获取客户信息。
     * @param inviteCode
     * @return
     */
    Customer getByInviteCode(String inviteCode);

    /**
     * 插入一个新的客户记录到数据库中。
     *
     * @param customer 要插入的客户对象
     * @return 如果插入成功，返回 true；否则返回 false
     * //t 插入操作可能因为数据库约束、数据完整性等原因失败
     * //r 返回一个 Boolean 值，表示插入操作是否成功
     * //n 该方法不处理任何异常，调用者需要自行处理可能的异常情况
     */
    Boolean insert(Customer customer);

    Boolean updateLevel(String customerId, String kycLevel);

    Boolean update(Customer customer);
}
