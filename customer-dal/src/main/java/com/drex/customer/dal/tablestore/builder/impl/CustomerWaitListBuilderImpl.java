package com.drex.customer.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.drex.customer.dal.tablestore.builder.CustomerWaitListBuilder;
import com.drex.customer.dal.tablestore.constant.Constant;
import com.drex.customer.dal.tablestore.model.CustomerWaitList;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2025/5/12 18:51
 * @description:
 */
@Repository
public class CustomerWaitListBuilderImpl extends WideColumnStoreBuilder<CustomerWaitList> implements CustomerWaitListBuilder {

    @Override
    public String getTableName() {
        return Constant.TABLE_NAME_CUSTOMER_WAIT_LIST;
    }

    @PostConstruct
    public void init() {
        super.init(CustomerWaitList.class);
    }

    @Override
    public boolean insertOrUpdate(CustomerWaitList customerWaitList) {
        return super.putRow(customerWaitList);
    }

    @Override
    public CustomerWaitList getWaitListByAddressOrEmail(String email, String address, long startTime, long endTime) {
        BoolQuery.Builder queryBuilder = QueryBuilders.bool();
        if (StringUtils.isNotEmpty(email)) {
            queryBuilder.must(QueryBuilders.term(Constant.COLUMN_NAME_EMAIL, email));
        } else if (StringUtils.isNotBlank(address)) {
            queryBuilder.must(QueryBuilders.term(Constant.COLUMN_NAME_EVM_ADDRESS, address));
        }
        // 判断 COLUMN_NAME_CREATE_TIME 列，为空 或者值在 startTime 和 endTime 之间
        queryBuilder.should(QueryBuilders.range(Constant.COLUMN_NAME_CREATE_TIME).greaterThan(startTime).lessThanOrEqual(endTime));
        queryBuilder.should(QueryBuilders.bool().mustNot(QueryBuilders.exists(Constant.COLUMN_NAME_CREATE_TIME)));
        queryBuilder.minimumShouldMatch(1);

        CustomerWaitList customerWaitList = searchOne(queryBuilder.build(), "search_customer_wait_list");
        return customerWaitList;
    }
}
