package com.drex.customer.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import com.drex.customer.dal.tablestore.constant.Constant;
import lombok.Data;

import java.io.Serializable;

@Table(name = Constant.TABLE_NAME_CUSTOMER_REFERRAL)
@Data
public class CustomerReferral implements Serializable {

    @SearchIndex(name = Constant.INDEX_NAME_CUSTOMER_REFERRAL_INDEX, column = Constant.COLUMN_NAME_CUSTOMER_ID)
    @PartitionKey(name = Constant.COLUMN_NAME_CUSTOMER_ID, type = PartitionKey.Type.STRING)
    private String customerId;

    @PartitionKey(name = Constant.COLUMN_NAME_BUSINESS_TYPE, type = PartitionKey.Type.STRING)
    private String businessType;

    @SearchIndex(name = Constant.INDEX_NAME_CUSTOMER_REFERRAL_INDEX, column = Constant.COLUMN_NAME_REFERRER_ID)
    @Column(name = Constant.COLUMN_NAME_REFERRER_ID, type = Column.Type.STRING)
    private String referrerId;

    @Column(name = Constant.COLUMN_NAME_CREATED, type = Column.Type.INTEGER)
    private Long created;

    @SearchIndex(name = Constant.INDEX_NAME_CUSTOMER_REFERRAL_INDEX, column = Constant.COLUMN_NAME_STATUS)
    @Column(name = Constant.COLUMN_NAME_STATUS)
    private String status;
}
