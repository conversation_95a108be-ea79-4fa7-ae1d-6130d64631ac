package com.drex.customer.dal.tablestore.model;


import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: joseph.xiang
 * @date: 2024/5/15 17:22
 * @desc: cdk兑换表
 */
@Data
@Accessors(chain = true)
@Table(name = "customer_code_transaction")
public class CustomerCodeTransaction implements Serializable {
    public static final String TABLE_NAME = "customer_code_transaction";

    public static final String INDEX_CUSTOMER_CODES_TRAN_DEFAULT_INDEX = "idx_customer_codes_tran_default_index_v1";
    public static final String IDX_CUSTOMER_CODE_TRAN_BY_SCENE = "idx_customer_code_transaction_by_scene_v1";
    public static final String IDX_CUSTOMER_CODE_TRAN_BY_CODE_USER = "idx_customer_code_transaction_by_code_user_v1";


    @SearchIndex(name = INDEX_CUSTOMER_CODES_TRAN_DEFAULT_INDEX, column = "user_id")
    @Index(name = IDX_CUSTOMER_CODE_TRAN_BY_SCENE, pkColumn = "user_id", pkValue = 1)
    @Index(name = IDX_CUSTOMER_CODE_TRAN_BY_CODE_USER, pkColumn = "user_id", pkValue = 2)
    @PartitionKey(name = "user_id")
    private String userId;

    @SearchIndex(name = INDEX_CUSTOMER_CODES_TRAN_DEFAULT_INDEX, column = "use_time", fieldType = FieldType.LONG)
    @Index(name = IDX_CUSTOMER_CODE_TRAN_BY_SCENE, pkColumn = "use_time", pkValue = 2)
    @Index(name = IDX_CUSTOMER_CODE_TRAN_BY_CODE_USER, pkColumn = "use_time", pkValue = 3)
    @PartitionKey(name = "use_time", type = PartitionKey.Type.INTEGER)
    private Date useTime;

    @SearchIndex(name = INDEX_CUSTOMER_CODES_TRAN_DEFAULT_INDEX, column = "code")
    @Index(name = IDX_CUSTOMER_CODE_TRAN_BY_SCENE, pkColumn = "code", pkValue = 3)
    @Index(name = IDX_CUSTOMER_CODE_TRAN_BY_CODE_USER, pkColumn = "code", pkValue = 1)
    @PartitionKey(name = "code")
    private String code;

    @SearchIndex(name = INDEX_CUSTOMER_CODES_TRAN_DEFAULT_INDEX, column = "owner_id")
    @Index(name = IDX_CUSTOMER_CODE_TRAN_BY_SCENE, dfColumn = "owner_id")
    @Column(name = "owner_id", isDefined = true)
    private String ownerId;



    @SearchIndex(name = INDEX_CUSTOMER_CODES_TRAN_DEFAULT_INDEX, column = "type")
    @Index(name = IDX_CUSTOMER_CODE_TRAN_BY_SCENE, dfColumn = "type")
    @Column(name = "type", isDefined = true)
    private String type;

    @SearchIndex(name = INDEX_CUSTOMER_CODES_TRAN_DEFAULT_INDEX, column = "scene")
    @Index(name = IDX_CUSTOMER_CODE_TRAN_BY_SCENE, pkColumn = "scene", pkValue = 0)

    @Column(name = "scene", isDefined = true)
    private String scene;

    @SearchIndex(name = INDEX_CUSTOMER_CODES_TRAN_DEFAULT_INDEX, column = "level", fieldType = FieldType.LONG)
    @Index(name = IDX_CUSTOMER_CODE_TRAN_BY_SCENE, dfColumn = "level")
    @Column(name = "level", isDefined = true)
    private Integer level;

    @SearchIndex(name = INDEX_CUSTOMER_CODES_TRAN_DEFAULT_INDEX, column = "user_role")
    @Index(name = IDX_CUSTOMER_CODE_TRAN_BY_SCENE, dfColumn = "user_role")
    @Column(name = "user_role", isDefined = true)
    private String userRole;
}
