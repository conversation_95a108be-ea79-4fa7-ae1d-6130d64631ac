package com.drex.customer.dal.tablestore;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component("idUtils")
public class IdUtils {

    private static Snowflake snowflake;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    public String nextId() {
        if(snowflake == null){
            long workerId = redisTemplate.opsForValue().increment("seq:workId");
            snowflake = IdUtil.getSnowflake(workerId % 32, 1);
        }
        return snowflake.nextIdStr();
    }
}
