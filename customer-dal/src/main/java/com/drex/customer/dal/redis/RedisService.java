package com.drex.customer.dal.redis;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;

public interface RedisService {

    /**
     * 删除属性
     *
     * @param key key值
     * @return 返回成功
     */
    Boolean del(String key);

    /**
     * 批量删除属性
     *
     * @param keys key值集合
     * @return 返回删除数量
     */
    Long del(List<String> keys);

    /**
     * 设置过期时间
     *
     * @param key  key值
     * @param time 时间戳
     * @return 返回成功
     */
    Boolean expire(String key, final long time);

    /**
     * 设置过期时间
     *
     * @param key  key值
     * @param time 时间戳
     * @return 返回成功
     */
    Boolean expireAt(String key, Date time);

    /**
     * 获取过期时间
     *
     * @param key key值
     * @return 返回时间戳
     */
    Long getExpire(String key);

    /**
     * 判断key是否存在
     *
     * @param key key值
     * @return 返回
     */
    Boolean hasKey(String key);

    /**
     * 按delta递增
     *
     * @param key   key值
     * @param delta delta值
     * @return 返回递增后结果
     */
    Long increaseBy(String key, long delta);

    /**
     * 按delta递减
     *
     * @param key   key值
     * @param delta delta值
     * @return 返回递减后结果
     */
    Long decreaseBy(String key, long delta);

    /**
     * 获取Hash结构中的属性
     *
     * @param key     外部key值
     * @param hashKey 内部key值
     * @return 返回内部key的value
     */
    Object hGet(String key, String hashKey);

    /**
     * 向Hash结构中放入一个属性
     *
     * @param key     外部key
     * @param hashKey 内部key
     * @param value   内部key的value
     * @param time    过期时间
     * @return 返回是否成功
     */
    Boolean hSet(String key, String hashKey, Object value, long time);

    /**
     * 向Hash结构中放入一个属性
     *
     * @param key     外部key
     * @param hashKey 内部key
     * @param value   内部key的value
     */
    void hSet(String key, String hashKey, Object value);

    /**
     * Map方式获取整个Hash结构
     *
     * @param key 外部key值
     * @return 返回hashMap
     */
    Map hGetAllMap(String key);

    /**
     * List方式获取整个Hash结构
     *
     * @param key 外部key值
     * @return 返回List
     */
    List hGetAllList(String key);

    /**
     * 直接设置整个Hash结构
     *
     * @param key  外部key
     * @param map  hashMap值
     * @param time 过期时间
     * @return 返回是否成功
     */
    Boolean hSetAll(String key, Map<String, Object> map, long time);

    /**
     * 直接设置整个Hash结构
     *
     * @param key 外部key
     * @param map hashMap值
     */
    void hSetAll(String key, Map<String, ?> map);

    /**
     * 删除Hash结构中的属性
     *
     * @param key     外部key值
     * @param hashKey 内部key值
     */
    void hDel(String key, Object... hashKey);

    /**
     * 判断Hash结构中是否有该属性
     *
     * @param key     外部key
     * @param hashKey 内部key
     * @return 返回是否存在
     */
    Boolean hHasKey(String key, String hashKey);

    /**
     * Hash结构中属性递增
     *
     * @param key     外部key
     * @param hashKey 内部key
     * @param delta   递增条件
     * @return 返回递增后的数据
     */
    Long hIncrease(String key, String hashKey, Long delta);

    /**
     * Hash结构中属性递减
     *
     * @param key     外部key
     * @param hashKey 内部key
     * @param delta   递增条件
     * @return 返回递减后的数据
     */
    Long hDecrease(String key, String hashKey, Long delta);

    /**
     * 获取List结构的长度
     *
     * @param key key
     * @return 长度
     */
    Long lSize(String key);

    /**
     * 根据索引获取List中的属性
     *
     * @param key   key
     * @param index 索引
     * @return 对象
     */
    Object lIndex(String key, long index);

    /**
     * 从List结构中移除属性
     *
     * @param key   key
     * @param count 总量
     * @param value value
     * @return 返回删除后的长度
     */
    Long lRemove(String key, long count, Object value);

    Long lpushAll(String key, Collection<String> collection);

    Long lpush(String key, String value);

    Object lpop(String key);

    Object lpop(String key, Duration timeout);

    Object rpop(String key);

    List<String> lrange(String key);

    List<String> lrange(String key, long start, long end);

    Object rightPopAndLeftPush(String sourceKey, String destinationKey, Duration timeout);


    /**
     * 以map形式保存在缓存中，传一个map。
     *
     * @param key
     * @param map
     */
    public void saveMap(String key, Map<String, String> map);

    /**
     * 直接获取一个map类型的key返回一个map集合。
     *
     * @param key
     * @return
     */
    Map<String, String> getMap(String key);


    Long delete(String key);

    String lock(String key);

    String lock(String key, long expire);

    String tryLock(String key, long expire, long waitTime);

    String onceLock(String key, long expire);

    void unLock(String key);


    String save(String key, String value);

    boolean setIfAbsent(String key, String value, long expire);

    String get(String key);

    // TODO ???
    Set<String> keys(String patt);

    String scriptEval(String script, List<String> keys, Object... argv);

    long ttl(String key, TimeUnit timeUnit);
}
