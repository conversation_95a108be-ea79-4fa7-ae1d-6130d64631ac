package com.drex.customer.dal.tablestore.builder;

import com.drex.customer.api.response.PassportDTO;
import com.drex.customer.dal.tablestore.model.Passport;
import com.kikitrade.framework.common.model.TokenPage;

import java.util.List;

public interface PassportBuilder {

    Passport getByPassportId(String passportId);

    Boolean save(Passport passport);

    boolean updatePassport(Passport passport);

    boolean createPassport(Passport passport);

    List<Passport> getByHandleName(String handleName);

    TokenPage<Passport> listAllPassport(Integer limit, String nextToken);

    Passport getByInviteCode(String inviteCode);
}
