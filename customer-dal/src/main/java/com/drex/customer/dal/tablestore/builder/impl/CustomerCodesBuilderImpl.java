package com.drex.customer.dal.tablestore.builder.impl;

import com.alibaba.fastjson.JSON;
import com.alicloud.openservices.tablestore.AsyncClient;
import com.alicloud.openservices.tablestore.TableStoreCallback;
import com.alicloud.openservices.tablestore.model.*;
import com.alicloud.openservices.tablestore.model.condition.SingleColumnValueCondition;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.SearchResponse;
import com.alicloud.openservices.tablestore.model.search.groupby.GroupByBuilders;
import com.alicloud.openservices.tablestore.model.search.groupby.GroupByFieldResultItem;
import com.alicloud.openservices.tablestore.model.search.query.*;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.drex.customer.api.constants.CustomerConstants;
import com.drex.customer.dal.tablestore.builder.CustomerCodesBuilder;
import com.drex.customer.dal.tablestore.model.CustomerCodes;
import com.google.common.collect.Lists;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Future;

import static com.drex.customer.dal.tablestore.model.CustomerCodes.INDEX_CUSTOMER_CODES_DEFAULT_INDEX;

/**
 * <AUTHOR>
 * @create 2024/5/22 11:17 AM
 * @modify
 */
@Slf4j
@Repository
public class CustomerCodesBuilderImpl extends WideColumnStoreBuilder<CustomerCodes> implements CustomerCodesBuilder {

    @Resource
    private AsyncClient asyncClient;

    @PostConstruct
    public void init() {
        init(CustomerCodes.class);
    }
    @Override
    public boolean create(CustomerCodes customerCodesDO) {
        return putRow(customerCodesDO);
    }

    @Override
    public CustomerCodes getByCode(String code) {
        CustomerCodes customerCodesDO = new CustomerCodes().setCode(code);
        return getRow(customerCodesDO);
    }
    @Override
    public TokenPage getByCustomerId(String[] types, String customerId, String status, int limit, String nextToken) {
        BoolQuery query = new BoolQuery();
        List queryList = new ArrayList();
        TermQuery termQuery = new TermQuery();
        termQuery.setFieldName("customer_id");
        termQuery.setTerm(ColumnValue.fromString(customerId));
        queryList.add(termQuery);
        if (StringUtils.isNotBlank(status)) {
            TermQuery termQueryStatus = new TermQuery();
            termQueryStatus.setFieldName("status");
            termQueryStatus.setTerm(ColumnValue.fromString(status));
            queryList.add(termQueryStatus);
        }
        TermsQuery termsQuery = new TermsQuery();
        termsQuery.setFieldName("type");
        for (String type: types) {
            termsQuery.addTerm(ColumnValue.fromString(type));
        }
        queryList.add(termsQuery);
        query.setMustQueries(queryList);
        //即时间降序
        FieldSort fieldSort = new FieldSort("status");
        fieldSort.setOrder(SortOrder.ASC);
        FieldSort fieldSort2 = new FieldSort("sort");
        fieldSort2.setOrder(SortOrder.DESC);
        FieldSort fieldSort3 = new FieldSort("level");
        fieldSort3.setOrder(SortOrder.ASC);
        Sort sort = new Sort(Arrays.asList(fieldSort, fieldSort2, fieldSort3));
        return pageSearchQuery(query, sort, nextToken, limit, INDEX_CUSTOMER_CODES_DEFAULT_INDEX);
    }
    @Override
    public boolean updateStatusOff(CustomerCodes customerCodesDO) {
        Condition condition = new Condition(RowExistenceExpectation.EXPECT_EXIST);
        condition.setColumnCondition(new SingleColumnValueCondition("status",
                SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromString(CustomerConstants.Status.EFFECTIVE.getName())));
        return updateAndIncRow(customerCodesDO, List.of("status"), List.of(new Column("use_count", ColumnValue.fromLong(1L))), null, condition);
    }


    @Override
    public CustomerCodes getDefaultCode(String customerId) {
        BoolQuery query = QueryBuilders.bool()
                .must(QueryBuilders.term("customer_id", customerId))
                .must(QueryBuilders.term("type", CustomerConstants.CodeType.DEFAULT_INVITE.getName()))
                .build();
        CustomerCodes codesDO = searchOne(query, INDEX_CUSTOMER_CODES_DEFAULT_INDEX);
        return codesDO;
    }

    @Override
    public List<String> getByBatchCode(List<String> codes) {
        BoolQuery.Builder query = QueryBuilders.bool();
        for (String code: codes) {
            query.should(QueryBuilders.term("code", code));
        }
        Sort sort = new Sort(Arrays.asList(new FieldSort("code", SortOrder.DESC)));
        List<CustomerCodes> codesDO = search(query.build(), sort, 0, codes.size(), INDEX_CUSTOMER_CODES_DEFAULT_INDEX);
        return codesDO.stream().map(CustomerCodes::getCode).toList();
    }
    @Override
    public List<String> getByBatchSource(List<String> sources) {
        List<String> otsHas = new ArrayList<>();
        BoolQuery.Builder boolQuery = QueryBuilders.bool();
        for (String source: sources) {
            boolQuery.should(QueryBuilders.term("source", source));
        }
        String groupName1 = "source";
        // select sum(code) from customer_codes where ... group by source;
        SearchQuery searchQuery = SearchQuery.newBuilder().query(boolQuery)
                .limit(0)
                .addGroupBy(GroupByBuilders.groupByField(groupName1, "source").size(2000))
                .build();
        SearchResponse resp = searchGroupByMultiField(searchQuery, true, INDEX_CUSTOMER_CODES_DEFAULT_INDEX);
        log.info("getByBatchCode searchResponse={}", JSON.toJSONString(resp));
        //获取统计聚合结果。
        for (GroupByFieldResultItem item : resp.getGroupByResults().getAsGroupByFieldResult(groupName1).getGroupByFieldResultItems()) {
            otsHas.add(item.getKey());
        }
        return otsHas;
    }
    @Override
    public Page<CustomerCodes> getByCustomerId(String[] scenes, String[] types, String customerId, String status, int offset, int limit) {
        BoolQuery boolQuery = QueryBuilders.bool().build();
        List<Query> mustQueries = Lists.newArrayList();
        TermsQuery customerIdQuery = QueryBuilders.terms("customer_id").terms(customerId).build();
        mustQueries.add(customerIdQuery);

        if (ArrayUtils.isNotEmpty(types)) {
            TermsQuery typeQuery = QueryBuilders.terms("type").terms(types).build();
            mustQueries.add(typeQuery);
        }
        if (ArrayUtils.isNotEmpty(scenes)) {
            TermsQuery sceneQuery = QueryBuilders.terms("scene").terms(scenes).build();
            mustQueries.add(sceneQuery);
        }
        if (StringUtils.isNotBlank(status)) {
            TermQuery statusQuery = QueryBuilders.term("status", status).build();
            mustQueries.add(statusQuery);
        }
        boolQuery.setMustQueries(mustQueries);
        //即时间降序
        FieldSort fieldSort = new FieldSort("status");
        fieldSort.setOrder(SortOrder.ASC);
        FieldSort fieldSort2 = new FieldSort("sort");
        fieldSort2.setOrder(SortOrder.ASC);
        FieldSort fieldSort3 = new FieldSort("level");
        fieldSort3.setOrder(SortOrder.ASC);
        Sort sort = new Sort(Arrays.asList(fieldSort, fieldSort2, fieldSort3));
        return pageSearchQuery(boolQuery, sort, offset, limit, INDEX_CUSTOMER_CODES_DEFAULT_INDEX);
    }

    public TokenPage<CustomerCodes> listByScene(String scene, String nextToken, String customerId, int limit) {
        BoolQuery.Builder builder = QueryBuilders.bool()
                .must(QueryBuilders.term("scene", scene))
                .must(QueryBuilders.term("type", CustomerConstants.CodeType.INTERVAL_LIMIT.name()));
        if (StringUtils.isNotBlank(customerId)) {
            builder.must(QueryBuilders.term("customer_id", customerId));
        }
        return pageSearchQuery(builder.build(), null, nextToken, limit, INDEX_CUSTOMER_CODES_DEFAULT_INDEX);
    }

    public Future<Boolean> batchPutRowAsync(List<CustomerCodes> list) {
        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();
        for (CustomerCodes customerCodesDO : list) {
            PrimaryKeyBuilder pkBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            pkBuilder.addPrimaryKeyColumn("code", PrimaryKeyValue.fromString(customerCodesDO.getCode()));
            //设置数据表名称。
            RowUpdateChange rowUpdateChange = new RowUpdateChange(CustomerCodes.TABLE_NAME, pkBuilder.build());
            //添加一些列。
            rowUpdateChange.put(new Column("limit_number", ColumnValue.fromLong(customerCodesDO.getTotalLimitNumber())));
            rowUpdateChange.setCondition(new Condition(RowExistenceExpectation.EXPECT_EXIST));
            batchWriteRowRequest.addRowChange(rowUpdateChange);
        }

        return asyncClient.batchWriteRow(batchWriteRowRequest, (TableStoreCallback)null);
    }
}
