package com.drex.customer.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.MatchAllQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.drex.customer.api.request.CustomerBindQuery;
import com.drex.customer.dal.tablestore.builder.CustomerBindBuilder;
import com.drex.customer.dal.tablestore.constant.Constant;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.dal.tablestore.model.Passport;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/27 20:53
 * @description:
 */
@Component
public class CustomerBindBuilderImpl extends WideColumnStoreBuilder<CustomerBind> implements CustomerBindBuilder {
    @Override
    public String getTableName() {
        return Constant.TABLE_NAME_CUSTOMER_BIND;
    }

    @PostConstruct
    public void init() {
        super.init(CustomerBind.class);
    }

    @Override
    public boolean insert(CustomerBind customerBind) {
        return super.putRow(customerBind);
    }

    @Override
    public boolean delete(CustomerBind customerBind) {

        return super.deleteRow(customerBind);
    }

    @Override
    public boolean update(CustomerBind customerBind) {
        return super.updateRow(customerBind);
    }

    @Override
    public CustomerBind findByCustomerId(String customerId, String socialPlatform) {
        CustomerBind customerBind = new CustomerBind();
        customerBind.setCustomerId(customerId);
        customerBind.setSocialPlatform(socialPlatform);
        return getRow(customerBind);
    }

    @Override
    public List<CustomerBind> findByCustomerId(String customerId) {
        List<RangeQueryParameter> parameters = new ArrayList<>();
        parameters.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.fromString(customerId)));
        parameters.add(new RangeQueryParameter("social_platform", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return rangeQuery(parameters);
    }

    @Override
    public List<CustomerBind> findByCustomerId(String customerId, List<String> socialPlatforms) {
        List<CustomerBind> customerBinds = new ArrayList<>();
        socialPlatforms.forEach(socialPlatform -> {
            CustomerBind customerBind = new CustomerBind();
            customerBind.setCustomerId(customerId);
            customerBind.setSocialPlatform(socialPlatform);
            customerBinds.add(customerBind);
        });
        return batchGetRow(customerBinds);
    }

    @Override
    public CustomerBind findBySocialInfo(CustomerBindQuery query) {
        if (query == null || !StringUtils.hasText(query.getSocialPlatform())) {
            return null;
        }

        BoolQuery.Builder boolQueryBuilder = QueryBuilders.bool()
                .must(QueryBuilders.term("social_platform", query.getSocialPlatform()));

        int shouldConditionCount = 0;

        // 只有当参数不为空字符串时才添加到查询条件中
        if (StringUtils.hasText(query.getSocialUserId())) {
            boolQueryBuilder.should(QueryBuilders.term("social_user_id", query.getSocialUserId()));
            shouldConditionCount++;
        }

        if (StringUtils.hasText(query.getSocialUserName())) {
            boolQueryBuilder.should(QueryBuilders.term("social_handle_name", query.getSocialUserName()));
            shouldConditionCount++;
        }

        if (StringUtils.hasText(query.getSocialEmail())) {
            boolQueryBuilder.should(QueryBuilders.term("social_email", query.getSocialEmail()));
            shouldConditionCount++;
        }

        // 如果没有有效的should条件，直接返回null
        if (shouldConditionCount == 0) {
            return null;
        }

        BoolQuery boolQuery = boolQueryBuilder.minimumShouldMatch(1).build();
        return searchOne(boolQuery, CustomerBind.SEARCH_CUSTOMER_BIND);
    }

    @Override
    public TokenPage<CustomerBind> listAllCustomerBind(Integer limit, String nextToken) {
        MatchAllQuery.Builder builder = QueryBuilders.matchAll();
        Sort sort = new Sort(List.of(new FieldSort("passport_id", SortOrder.ASC)));
        return pageSearchQuery(builder.build(), sort, nextToken, limit, CustomerBind.SEARCH_CUSTOMER_BIND);
    }
}
