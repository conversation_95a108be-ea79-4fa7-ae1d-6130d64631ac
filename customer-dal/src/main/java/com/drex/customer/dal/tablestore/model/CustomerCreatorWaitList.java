package com.drex.customer.dal.tablestore.model;

import com.drex.customer.dal.tablestore.constant.Constant;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Table(name = Constant.TABLE_NAME_CUSTOMER_CREATOR_WAIT_LIST)
public class CustomerCreatorWaitList implements Serializable {

    @PartitionKey(name = Constant.COLUMN_NAME_ID)
    private String id;

    @Column(name = Constant.COLUMN_NAME_NAME, type = Column.Type.STRING)
    private String name;

    @Column(name = Constant.COLUMN_NAME_CONTACT_EMAIL, type = Column.Type.STRING)
    private String contactEmail;

    @Column(name = Constant.COLUMN_NAME_CONTACT_TELEGRAM, type = Column.Type.STRING)
    private String contactTelegram;

    @Column(name = Constant.COLUMN_NAME_CONTACT_DISCORD, type = Column.Type.STRING)
    private String contactDiscord;

    @Column(name = Constant.COLUMN_NAME_CONTACT_OTHER, type = Column.Type.STRING)
    private String contactOther;

    @Column(name = Constant.COLUMN_NAME_SOCIALS_INSTAGRAM, type = Column.Type.STRING)
    private String socialsInstagram;

    @Column(name = Constant.COLUMN_NAME_SOCIALS_TWITTER, type = Column.Type.STRING)
    private String socialsTwitter;

    @Column(name = Constant.COLUMN_NAME_SOCIALS_YOUTUBE, type = Column.Type.STRING)
    private String socialsYoutube;

    @Column(name = Constant.COLUMN_NAME_SOCIALS_TIKTOK, type = Column.Type.STRING)
    private String socialsTiktok;

    @Column(name = Constant.COLUMN_NAME_SOCIALS_TELEGRAM, type = Column.Type.STRING)
    private String socialsTelegram;

    @Column(name = Constant.COLUMN_NAME_SOCIALS_OTHER, type = Column.Type.STRING)
    private String socialsOther;

    @Column(name = Constant.COLUMN_NAME_CONTENT_TYPE, type = Column.Type.STRING)
    private String contentTypes;

    @Column(name = Constant.COLUMN_NAME_CONTENT_TYPE_OTHER, type = Column.Type.STRING)
    private String contentTypesOther;

    @Column(name = Constant.COLUMN_NAME_INTEREST_REASON, type = Column.Type.STRING)
    private String interestReasons;

    @Column(name = Constant.COLUMN_NAME_INTEREST_REASON_OTHER, type = Column.Type.STRING)
    private String interestReasonsOther;

    @Column(name = Constant.COLUMN_NAME_CREATE_TIME, type = Column.Type.INTEGER)
    private Long createTime;
}
