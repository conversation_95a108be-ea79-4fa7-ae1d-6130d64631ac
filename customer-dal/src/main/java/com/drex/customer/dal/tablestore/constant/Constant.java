package com.drex.customer.dal.tablestore.constant;

import lombok.Getter;

public interface Constant {
    String TABLE_NAME_CUSTOMER = "customer";
    String INDEX_NAME_CUSTOMER_EOA_INDEX = "search_customer_eoa_index";
    String INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX = "customer_invite_code_index";
    String TABLE_NAME_CUSTOMER_BIND = "customer_bind";

    String COLUMN_NAME_ID = "id";

    // 原有的常量保持不变
    String COLUMN_NAME_CUSTOMER_ID = "customer_id";
    String COLUMN_NAME_REGISTER_ADDRESS = "register_address";
    String COLUMN_NAME_EOA_ADDRESS = "eoa_address";
    String COLUMN_NAME_KYC_LEVEL = "kyc_level";
    String COLUMN_NAME_REGISTER_TIME = "register_time";
    String COLUMN_NAME_INVITE_CODE = "invite_code";
    String COLUMN_NAME_STATUS = "status";
    String COLUMN_NAME_CREATED = "created";
    String COLUMN_NAME_USERNAME = "username";
    String TABLE_NAME_CUSTOMER_WAIT_LIST = "customer_wait_list";
    String COLUMN_NAME_NAME = "name";
    String COLUMN_NAME_EMAIL = "email";

    String TABLE_NAME_CUSTOMER_WAIT_DEVELOPER_LIST = "customer_wait_developer_list";
    String COLUMN_NAME_CHAIN_ID = "chain_id";
    String COLUMN_NAME_SMART_ACCOUNT_ADDRESS = "smart_account_address";
    String COLUMN_NAME_AUTH_PROVIDER = "auth_provider";
    String COLUMN_NAME_CLIENT_IP = "client_ip";
    String COLUMN_NAME_DEVICE_INFO = "device_info";
    String COLUMN_NAME_CONNECT_ADDRESS = "connect_address";

    // 邀请关系表名
    String TABLE_NAME_CUSTOMER_REFERRAL = "customer_referral";
    String INDEX_NAME_CUSTOMER_REFERRAL_INDEX = "search_customer_referral";
    // 邀请人 ID
    String COLUMN_NAME_REFERRER_ID = "referrer_id";
    // 业务类型
    String COLUMN_NAME_BUSINESS_TYPE = "business_type";
    String COLUMN_NAME_PROJECT = "project";
    String COLUMN_NAME_EVM_ADDRESS = "evm_address";
    String COLUMN_NAME_CREATE_TIME = "create_time";
    String COLUMN_NAME_PROJECT_NAME = "project_name";
    String COLUMN_NAME_PROJECT_CATEGORY = "project_category";
    String COLUMN_NAME_PROJECT_DESCRIPTION = "project_description";
    String COLUMN_NAME_ALIGNMENT_WITH_TREX = "alignment_with_trex";
    String COLUMN_NAME_PROJECT_STAGE = "project_stage";
    String COLUMN_NAME_PROJECT_WEBSITE = "project_website";
    String COLUMN_NAME_PROJECT_TWITTER = "project_twitter";
    String COLUMN_NAME_PROJECT_COMMUNITY = "project_community";
    String COLUMN_NAME_PRIMARY_CONTACT_NAME = "primary_contact_name";
    String COLUMN_NAME_CONTACT_TELEGRAM_USERNAME = "contact_telegram";
    String COLUMN_NAME_SUPPORT_TYPE = "support_type";
    String COLUMN_NAME_USE_OF_FUNDS = "use_of_funds";
    String COLUMN_NAME_TEAM_BACKGROUND = "team_background";
    String COLUMN_NAME_SUPPORTING_DOCUMENTS = "supporting_documents";
    String COLUMN_NAME_ADDITIONAL_COMMENTS = "additional_comments";

    String TABLE_NAME_CUSTOMER_CREATOR_WAIT_LIST = "customer_creator_wait_list";
    String COLUMN_NAME_CONTACT_METHOD = "contact_method";
    String COLUMN_NAME_SOCIALS_OTHER = "socials_other";
    String COLUMN_NAME_CONTENT_TYPE = "content_type";
    String COLUMN_NAME_INTEREST_REASON = "interest_reason";
    String COLUMN_NAME_CONTACT_EMAIL = "contact_email";
    String COLUMN_NAME_CONTACT_TELEGRAM = "contact_telegram";
    String COLUMN_NAME_CONTACT_DISCORD = "contact_discord";
    String COLUMN_NAME_CONTACT_OTHER = "contact_other";
    String COLUMN_NAME_SOCIALS_INSTAGRAM = "socials_instagram";
    String COLUMN_NAME_SOCIALS_YOUTUBE = "socials_youtube";
    String COLUMN_NAME_SOCIALS_TIKTOK = "socials_tiktok";
    String COLUMN_NAME_SOCIALS_TELEGRAM = "socials_telegram";
    String COLUMN_NAME_CONTENT_TYPE_OTHER = "content_type_other";
    String COLUMN_NAME_INTEREST_REASON_OTHER = "interest_reason_other";
    String COLUMN_NAME_SOCIALS_TWITTER = "socials_twitter";
    String COLUMN_NAME_PROJECT_CATEGORY_OTHER = "project_category_other";
    String COLUMN_NAME_ECOSYSTEM_ALIGNMENT = "ecosystem_alignment";
    String COLUMN_NAME_SUPPORT_TYPE_OTHER = "support_type_other";
    String COLUMN_NAME_THIRD_USER_ID = "third_user_id";
    String COLUMN_NAME_USER_PICTURE = "user_picture";
    String COLUMN_NAME_USER_ID = "user_id";
    String COLUMN_NAME_LOWER_CONNECT_ADDRESS = "lower_connect_address";

    @Getter
    enum CommonStatus {

        valid(1),
        invalid(0)
        ;

        private int status;

        CommonStatus(int status) {
            this.status = status;
        }
    }
}
