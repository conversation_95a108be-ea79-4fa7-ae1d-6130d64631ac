package com.drex.customer.dal.tablestore.builder;

import com.drex.customer.dal.tablestore.model.PassportConnect;

import java.util.List;

public interface PassportConnectBuilder {

    PassportConnect getByIdentifier(String walletAddress, String subConnectProvider);

    List<PassportConnect> getByIdentifier(String walletAddress);

    Boolean save(PassportConnect passportConnect);

    List<PassportConnect> getByPassportId(String passportId);

    List<PassportConnect> listAllPassportConnect();

    PassportConnect getByPassportIdAndWalletAddress(String passportId, String walletAddress);

    Boolean updatePassportConnect(PassportConnect passportConnect);
}
