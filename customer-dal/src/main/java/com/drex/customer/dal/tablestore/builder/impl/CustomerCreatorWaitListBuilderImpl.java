package com.drex.customer.dal.tablestore.builder.impl;

import cn.hutool.core.util.IdUtil;
import com.drex.customer.dal.tablestore.builder.CustomerCreatorWaitListBuilder;
import com.drex.customer.dal.tablestore.constant.Constant;
import com.drex.customer.dal.tablestore.model.CustomerCreatorWaitList;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Repository;

@Repository
public class CustomerCreatorWaitListBuilderImpl extends WideColumnStoreBuilder<CustomerCreatorWaitList> implements CustomerCreatorWaitListBuilder {

    @Override
    public String getTableName() {
        return Constant.TABLE_NAME_CUSTOMER_CREATOR_WAIT_LIST;
    }

    @PostConstruct
    public void init() {
        super.init(CustomerCreatorWaitList.class);
    }

    @Override
    public boolean insertOrUpdate(CustomerCreatorWaitList customerCreatorWaitList) {
        customerCreatorWaitList.setId(IdUtil.objectId());
        return super.putRow(customerCreatorWaitList);
    }
}
